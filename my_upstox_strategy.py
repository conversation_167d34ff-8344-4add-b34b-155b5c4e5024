#!/usr/bin/env python3
"""
Simple EMA Crossover Strategy for Upstox via OpenAlgo
This is a template strategy that you can customize for your needs
"""

from openalgo import api
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta

class UpstoxEMAStrategy:
    def __init__(self, api_key, symbol="RELIANCE", exchange="NSE"):
        """Initialize the strategy"""
        self.api_key = api_key
        self.symbol = symbol
        self.exchange = exchange
        self.product = "MIS"  # Intraday
        self.quantity = 1  # Start with 1 share for testing
        
        # EMA parameters
        self.fast_ema = 5
        self.slow_ema = 10
        
        # Position tracking
        self.position = 0  # 0 = no position, 1 = long, -1 = short
        
        # Initialize OpenAlgo client
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:6000')
        
        print(f"🚀 Initialized EMA Strategy for {self.symbol}")
        print(f"📊 Fast EMA: {self.fast_ema}, Slow EMA: {self.slow_ema}")
        print(f"💰 Quantity: {self.quantity}")
    
    def get_historical_data(self, days=7):
        """Get historical data for analysis"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.symbol,
                exchange=self.exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                print("⚠️ No historical data received")
                return None
                
            return df
            
        except Exception as e:
            print(f"❌ Error getting historical data: {e}")
            return None
    
    def calculate_ema_signals(self, df):
        """Calculate EMA crossover signals"""
        if df is None or len(df) < max(self.fast_ema, self.slow_ema):
            return None, None
        
        # Calculate EMAs
        df['ema_fast'] = df['close'].ewm(span=self.fast_ema).mean()
        df['ema_slow'] = df['close'].ewm(span=self.slow_ema).mean()
        
        # Generate signals
        df['signal'] = 0
        df['signal'][self.fast_ema:] = np.where(
            df['ema_fast'][self.fast_ema:] > df['ema_slow'][self.fast_ema:], 1, -1
        )
        
        # Detect crossovers
        df['position'] = df['signal'].diff()
        
        return df['position'].iloc[-1], df
    
    def place_order(self, action):
        """Place order through OpenAlgo"""
        try:
            response = self.client.placesmartorder(
                strategy="EMA Crossover Upstox",
                symbol=self.symbol,
                action=action,
                exchange=self.exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.quantity,
                position_size=self.quantity if action == "BUY" else -self.quantity
            )
            
            print(f"📋 Order Response: {response}")
            return response
            
        except Exception as e:
            print(f"❌ Error placing order: {e}")
            return None
    
    def run_strategy(self):
        """Main strategy execution loop"""
        print("🎯 Starting EMA Crossover Strategy...")
        print("Press Ctrl+C to stop")
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Get historical data
                df = self.get_historical_data()
                if df is None:
                    time.sleep(30)
                    continue
                
                # Calculate signals
                signal, df_with_signals = self.calculate_ema_signals(df)
                
                if df_with_signals is None:
                    print("⚠️ Not enough data for analysis")
                    time.sleep(30)
                    continue
                
                # Get current price and EMAs
                current_price = df['close'].iloc[-1]
                fast_ema = df_with_signals['ema_fast'].iloc[-1]
                slow_ema = df_with_signals['ema_slow'].iloc[-1]
                
                print(f"💰 Current Price: {current_price:.2f}")
                print(f"📈 Fast EMA ({self.fast_ema}): {fast_ema:.2f}")
                print(f"📉 Slow EMA ({self.slow_ema}): {slow_ema:.2f}")
                print(f"📊 Current Position: {self.position}")
                
                # Trading logic
                if signal == 2 and self.position <= 0:  # Bullish crossover
                    print("🟢 BUY SIGNAL - Fast EMA crossed above Slow EMA")
                    response = self.place_order("BUY")
                    if response:
                        self.position = 1
                        
                elif signal == -2 and self.position >= 0:  # Bearish crossover
                    print("🔴 SELL SIGNAL - Fast EMA crossed below Slow EMA")
                    response = self.place_order("SELL")
                    if response:
                        self.position = -1
                
                else:
                    print("⚪ No signal - Holding current position")
                
                # Wait before next iteration
                print("⏳ Waiting 30 seconds...")
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\n🛑 Strategy stopped by user")
        except Exception as e:
            print(f"❌ Strategy error: {e}")

def main():
    """Main function to run the strategy"""
    print("🎯 Upstox EMA Crossover Strategy")
    print("=" * 40)
    
    # Configuration
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    # Initialize and run strategy
    strategy = UpstoxEMAStrategy(api_key=api_key, symbol=symbol)
    strategy.run_strategy()

if __name__ == "__main__":
    main()
