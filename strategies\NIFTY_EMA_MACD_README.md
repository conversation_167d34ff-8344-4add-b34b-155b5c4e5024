# NIFTY EMA-MACD Options Strategy

## 🎯 Overview

A sophisticated options trading strategy that combines **EMA trend analysis** with **MACD momentum signals** to trade NIFTY options with high-probability setups. The strategy analyzes NIFTY SPOT price movements and executes ITM/ATM Call (CE) or Put (PE) options based on confluence signals.

## 📊 Strategy Logic

### Entry Conditions

#### 🟢 CE (Call) Entry Signal
- **EMA Alignment**: EMA(9) > EMA(21) > EMA(34) - All EMAs sloping upward
- **MACD Confluence**: MACD line crosses above Signal line (bullish crossover)
- **Execution**: Buy ITM/ATM Call options

#### 🔴 PE (Put) Entry Signal  
- **EMA Alignment**: EMA(9) < EMA(21) < EMA(34) - All EMAs sloping downward
- **MACD Confluence**: MACD line crosses below Signal line (bearish crossover)
- **Execution**: Buy ITM/ATM Put options

### Risk Management

- **Stop Loss**: 15 points or swing-based (whichever is more conservative)
- **Trailing Stop**: Activates after 10 points profit
- **Position Sizing**: Configurable lot quantities
- **Swing Analysis**: 20-period lookback for swing high/low detection

## 🚀 Quick Start

### Method 1: Quick Start Script
```bash
python run_nifty_ema_macd.py
```

### Method 2: Direct Execution
```bash
python nifty_ema_macd_options_strategy.py
```

## 📋 Configuration Options

### Timeframes
- **1 minute**: High-frequency scalping
- **2 minutes**: Balanced approach
- **3 minutes**: Medium-term signals
- **5 minutes**: Swing trading

### Trading Modes
- **Paper Trading**: Safe testing mode (recommended for beginners)
- **Live Trading**: Real money execution

### Parameters
- **EMA Periods**: 9, 21, 34 (customizable)
- **MACD Settings**: Fast(9), Slow(13), Signal(9)
- **Lot Size**: 1-5 lots (configurable)
- **Stop Loss**: 15 points (adjustable)
- **Trailing Trigger**: 10 points (adjustable)

## 🛠️ Installation & Setup

### Prerequisites
1. OpenAlgo platform running
2. UPSTOX broker account connected
3. Valid OpenAlgo API key
4. Python 3.7+ with required packages

### Setup Steps
1. Ensure OpenAlgo is running on `http://127.0.0.1:6000`
2. Login to OpenAlgo with your UPSTOX credentials
3. Generate API key from OpenAlgo dashboard
4. Place strategy files in the `strategies/` directory
5. Run the strategy using quick start script

## 📈 Strategy Features

### ✅ Core Features
- **Multi-timeframe Support**: 1m, 2m, 3m, 5m
- **EMA Trend Analysis**: Triple EMA confluence system
- **MACD Momentum**: Custom MACD(9,13) signals
- **Options Strike Selection**: Intelligent ITM/ATM selection
- **Advanced Risk Management**: Swing-based SL + trailing
- **Paper Trading Mode**: Risk-free testing
- **Comprehensive Logging**: Detailed execution logs

### 🔧 Advanced Features
- **Real-time Monitoring**: Live P&L tracking
- **Position Management**: Automatic position sizing
- **Error Handling**: Robust error recovery
- **Safety Limits**: Maximum daily trades/losses
- **Performance Tracking**: Trade history and metrics

## 📊 NIFTY Options Specifications

| Parameter | Value |
|-----------|-------|
| **Underlying** | NIFTY (NSE) |
| **Options Exchange** | NFO |
| **Lot Size** | 25 shares per lot |
| **Strike Interval** | 50 points |
| **Expiry** | Weekly (Thursday) |
| **Symbol Format** | NIFTY28NOV2424650CE |

## 🎮 Usage Examples

### Example 1: Paper Trading (Recommended)
```python
from nifty_ema_macd_options_strategy import NiftyEMAMACDOptionsStrategy

# Initialize strategy
strategy = NiftyEMAMACDOptionsStrategy(api_key="your-api-key")

# Configure for paper trading
strategy.set_timeframe("1m")
strategy.set_paper_trading(True)
strategy.quantity = 1

# Run strategy
strategy.run_strategy()
```

### Example 2: Live Trading
```python
# Initialize for live trading
strategy = NiftyEMAMACDOptionsStrategy(api_key="your-api-key")
strategy.set_timeframe("5m")
strategy.set_paper_trading(False)  # LIVE TRADING
strategy.quantity = 2

# Run with caution
strategy.run_strategy()
```

## 📝 Sample Trade Scenarios

### Bullish Setup (CE Entry)
```
NIFTY: 24,650
EMA 9: 24,655 (rising)
EMA 21: 24,640 (rising)  
EMA 34: 24,620 (rising)
MACD: Bullish crossover detected

Action: Buy NIFTY28NOV2424600CE (ITM)
Entry: ₹85
Stop Loss: ₹70 (15 points)
Trailing: Activates at ₹95
```

### Bearish Setup (PE Entry)
```
NIFTY: 24,650
EMA 9: 24,645 (falling)
EMA 21: 24,660 (falling)
EMA 34: 24,680 (falling)  
MACD: Bearish crossover detected

Action: Buy NIFTY28NOV2424700PE (ITM)
Entry: ₹78
Stop Loss: ₹63 (15 points)
Trailing: Activates at ₹88
```

## ⚠️ Risk Warnings

### Important Disclaimers
- **High Risk**: Options trading involves substantial risk
- **Paper Trading First**: Always test with paper trading
- **Market Hours**: Strategy works during market hours only
- **Volatility Impact**: High volatility can affect performance
- **Capital Requirements**: Ensure adequate margin for options

### Safety Recommendations
1. **Start Small**: Begin with 1 lot
2. **Paper Trade**: Test thoroughly before live trading
3. **Monitor Closely**: Don't leave strategy unattended
4. **Set Limits**: Define maximum daily loss limits
5. **Market Conditions**: Avoid during major news events

## 📊 Performance Monitoring

### Key Metrics Tracked
- **Win Rate**: Percentage of profitable trades
- **Average P&L**: Average profit/loss per trade
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Sharpe Ratio**: Risk-adjusted returns
- **Total Trades**: Number of executed trades

### Log Files
- **Strategy Log**: `nifty_ema_macd_strategy.log`
- **Trade History**: Detailed trade records
- **Error Log**: System errors and warnings

## 🔧 Troubleshooting

### Common Issues

#### Connection Problems
```
❌ Error: Could not connect to OpenAlgo
Solution: Ensure OpenAlgo is running on port 6000
```

#### Authentication Issues
```
❌ Error: Invalid API key
Solution: Generate new API key from OpenAlgo dashboard
```

#### Data Issues
```
❌ Error: No NIFTY data received
Solution: Check market hours and broker connection
```

#### Option Price Issues
```
❌ Error: Could not get option price
Solution: Verify option symbol format and market hours
```

## 📞 Support & Documentation

### Additional Resources
- **OpenAlgo Documentation**: [docs.openalgo.in](https://docs.openalgo.in)
- **UPSTOX API Docs**: [upstox.com/developer](https://upstox.com/developer)
- **Strategy Configuration**: `nifty_ema_macd_config.py`
- **Quick Start Guide**: `run_nifty_ema_macd.py`

### Configuration Files
- **Main Strategy**: `nifty_ema_macd_options_strategy.py`
- **Configuration**: `nifty_ema_macd_config.py`
- **Quick Start**: `run_nifty_ema_macd.py`
- **Documentation**: `NIFTY_EMA_MACD_README.md`

## 📈 Version History

### v1.0 (Current)
- ✅ EMA(9,21,34) trend analysis
- ✅ MACD(9,13) momentum signals
- ✅ Multi-timeframe support
- ✅ Advanced risk management
- ✅ Paper trading mode
- ✅ Comprehensive logging
- ✅ Options strike selection
- ✅ Trailing stop loss

### Planned Features (v1.1)
- 🔄 Backtesting engine
- 🔄 Performance analytics
- 🔄 Multi-timeframe confirmation
- 🔄 Volume analysis
- 🔄 Volatility filters
- 🔄 Notification system

---

**⚠️ Disclaimer**: This strategy is for educational purposes. Trading involves risk of loss. Always test with paper trading first and never risk more than you can afford to lose.
