# NIFTY Options Renko SuperTrend Strategy

A comprehensive NIFTY Options trading strategy with Renko-based entries, SuperTrend filter, and options-specific risk management, designed for OpenAlgo with Upstox integration.

## 🎯 Strategy Overview

This strategy is specifically designed for NIFTY Options trading with:

1. **Automatic Strike Selection** - ATM, OTM1, OTM2, ITM1, ITM2
2. **Dynamic Expiry Management** - Current week, Next week, Monthly
3. **SuperTrend Filter** for directional bias (CE vs PE)
4. **Renko-based Entries** with NIFTY-appropriate box sizes (25-100)
5. **Options-specific Risk Management** - Time decay, Greeks monitoring
6. **Real-time Price Monitoring** with 1-second updates
7. **Multiple Strategy Presets** - Conservative, Aggressive, Scalping, Weekly

## 📁 Files for NIFTY Options

- `nifty_options_renko_strategy.py` - Basic NIFTY options implementation
- `nifty_options_enhanced.py` - Advanced version with full features
- `nifty_options_config.py` - Configuration file for options parameters
- `run_nifty_options.py` - Simple launcher with preset modes
- `NIFTY_OPTIONS_README.md` - This documentation

## 🚀 Quick Start

### Option 1: Simple Launcher (Recommended)
```bash
python run_nifty_options.py
```

### Option 2: Enhanced Version (Full features)
```bash
python nifty_options_enhanced.py
```

### Option 3: Basic Version
```bash
python nifty_options_renko_strategy.py
```

## ⚙️ NIFTY Options Configuration

### Basic Parameters

| Parameter | Description | Default | Options |
|-----------|-------------|---------|---------|
| `OPTION_TYPE` | Type of options to trade | "BOTH" | "CE", "PE", "BOTH" |
| `STRIKE_SELECTION` | Strike selection strategy | "ATM" | "ATM", "OTM1", "OTM2", "ITM1", "ITM2" |
| `EXPIRY_PREFERENCE` | Expiry preference | "CURRENT" | "CURRENT", "NEXT", "MONTHLY" |
| `RENKO_BOX_SIZE` | Box size for NIFTY | 50 | 25-100 |
| `QUANTITY` | Number of lots | 1 | 1+ |
| `STOP_LOSS_PERCENTAGE` | Stop loss % of entry | 50 | 30-70 |

### NIFTY Specifications

| Specification | Value |
|---------------|-------|
| **Lot Size** | 25 shares per lot |
| **Strike Interval** | 50 points |
| **Exchange** | NFO (NSE F&O) |
| **Underlying** | NIFTY (NSE) |
| **Expiry** | Weekly (Thursday), Monthly (Last Thursday) |

### Symbol Format

NIFTY options in OpenAlgo use this format:
- **Original Upstox**: "NIFTY 24650 CE 28 NOV 24"
- **OpenAlgo Format**: "NIFTY28NOV2424650CE"

## 🎮 Trading Modes

### 1. Quick Start Modes

#### CE Options Only
- Trades only Call options
- Bullish bias strategy
- Good for trending up markets

#### PE Options Only
- Trades only Put options
- Bearish bias strategy
- Good for trending down markets

#### Both CE & PE
- SuperTrend decides direction
- Trades both calls and puts
- Suitable for volatile markets

### 2. Strategy Presets

#### Conservative Mode
```python
{
    "strike_selection": "ATM",
    "renko_box_size": 75,
    "stop_loss_percentage": 40,
    "time_decay_exit_minutes": 45,
    "quantity": 1
}
```

#### Aggressive Mode
```python
{
    "strike_selection": "OTM1",
    "renko_box_size": 25,
    "stop_loss_percentage": 60,
    "time_decay_exit_minutes": 20,
    "quantity": 2
}
```

#### Weekly Expiry Mode
```python
{
    "expiry_preference": "CURRENT",
    "strike_selection": "OTM1",
    "avoid_expiry_day": True,
    "time_decay_exit_minutes": 60
}
```

## 📊 Options Strategy Logic

### Entry Conditions

1. **SuperTrend Signal**:
   - Bullish SuperTrend → Buy CE options
   - Bearish SuperTrend → Buy PE options

2. **Strike Selection**:
   - ATM: Closest to current NIFTY price
   - OTM1: 1 strike out of money (CE: +50, PE: -50)
   - OTM2: 2 strikes out of money (CE: +100, PE: -100)
   - ITM1: 1 strike in the money (CE: -50, PE: +50)

3. **Renko Confirmation**:
   - Wait for Renko brick formation in trend direction
   - Box size adjusted for NIFTY volatility

### Exit Conditions

1. **Stop Loss**: 50% of entry price (configurable)
2. **Time Decay**: Exit if no movement within time limit
3. **Maximum Loss**: Absolute rupee loss limit
4. **Expiry Day**: Square off before market close
5. **Market Close**: Auto square-off

### Re-entry Logic

After stop loss hit:
- Monitor NIFTY price movement
- Re-enter when price moves favorably by box size amount
- No SuperTrend dependency for re-entries

## 🛡️ Risk Management

### Options-Specific Risks

1. **Time Decay (Theta)**:
   - Options lose value as expiry approaches
   - Strategy exits positions showing time decay
   - Configurable time-based exit rules

2. **Volatility Risk**:
   - Options prices affected by implied volatility
   - Strategy adjusts box sizes based on volatility
   - VIX-based adjustments (if available)

3. **Liquidity Risk**:
   - Filters for minimum volume and open interest
   - Avoids illiquid strikes
   - Price validation checks

### Safety Features

- **Paper Trading Mode**: Test without real money
- **Daily Loss Limits**: Stop trading after daily loss limit
- **Position Size Limits**: Maximum lots per trade
- **Expiry Day Avoidance**: Configurable expiry day trading
- **Price Validation**: Reasonable option price checks

## 📈 Performance Monitoring

### Real-time Metrics
- Current NIFTY price and ATM strike
- Option symbol, strike, and expiry
- Entry price, current price, P&L
- Time to expiry and holding time
- Greeks (if available)

### Historical Analysis
- Win rate and average P&L
- Maximum drawdown
- Average holding time
- Best and worst trades
- Monthly/weekly performance

## 🔧 Advanced Configuration

### Volatility-Based Adjustments

```python
VIX_BASED_ADJUSTMENTS = {
    "low_vix": {  # VIX < 15
        "renko_box_size_multiplier": 0.8,
        "stop_loss_multiplier": 0.8
    },
    "high_vix": {  # VIX > 25
        "renko_box_size_multiplier": 1.3,
        "stop_loss_multiplier": 1.2
    }
}
```

### NIFTY Level Adjustments

```python
NIFTY_LEVEL_CONFIGS = {
    "below_20000": {"renko_box_size": 40},
    "20000_25000": {"renko_box_size": 50},
    "above_25000": {"renko_box_size": 60}
}
```

## 📋 Prerequisites

### Required
1. **OpenAlgo installed and running**
2. **Upstox account with F&O enabled**
3. **Valid OpenAlgo API key**
4. **Sufficient margin for options trading**
5. **Understanding of options risks**

### NIFTY Options Requirements
- **F&O Account**: Required for options trading
- **Margin**: Sufficient margin for option purchases
- **Knowledge**: Understanding of options Greeks, time decay
- **Risk Tolerance**: Options can lose 100% value

## 🚨 Important Warnings

### Before Live Trading
1. **Test extensively** in paper trading mode
2. **Understand options risks** completely
3. **Start with small position sizes**
4. **Monitor positions actively**
5. **Have exit strategies ready**

### Options-Specific Risks
- **Total Loss Possible**: Options can expire worthless
- **Time Decay**: Value decreases as expiry approaches
- **Volatility Impact**: Prices affected by market volatility
- **Liquidity Risk**: Some strikes may have low liquidity
- **Gap Risk**: Options can gap significantly

## 📊 Example Scenarios

### Scenario 1: Bullish SuperTrend Signal
```
NIFTY Price: 24,600
SuperTrend: Bullish signal
Action: Buy 24650 CE (ATM Call)
Entry: ₹120
Stop Loss: ₹60 (50% of entry)
Target: Based on Renko movement
```

### Scenario 2: Bearish SuperTrend Signal
```
NIFTY Price: 24,600
SuperTrend: Bearish signal
Action: Buy 24550 PE (ATM Put)
Entry: ₹110
Stop Loss: ₹55 (50% of entry)
Target: Based on Renko movement
```

### Scenario 3: Re-entry After Stop Loss
```
Previous: 24650 CE stopped out at ₹60
NIFTY moves to 24,650 (50 points up from SL level)
Action: Re-enter 24700 CE
New Entry: ₹95
New Stop Loss: ₹47.50
```

## 🔄 Maintenance and Updates

### Daily Tasks
1. **Check margin requirements**
2. **Review overnight positions**
3. **Monitor expiry dates**
4. **Update strike prices if needed**
5. **Review performance metrics**

### Weekly Tasks
1. **Roll over expiring positions**
2. **Adjust strategy parameters**
3. **Review win/loss ratios**
4. **Update configuration if needed**
5. **Backup trade history**

## 📞 Troubleshooting

### Common Issues

#### "Option symbol not found"
- Check if symbol format is correct
- Verify strike price exists
- Ensure expiry date is valid
- Check if option is actively traded

#### "Insufficient margin"
- Check account margin
- Reduce position size
- Use paper trading mode
- Contact broker for margin requirements

#### "No option price data"
- Check market hours
- Verify option liquidity
- Try different strike prices
- Check network connection

### Debug Mode
Enable detailed logging:
```python
LOG_LEVEL = "DEBUG"
```

## 📜 Disclaimer

**This strategy is for educational purposes only.**

### Important Notes
- **High Risk**: Options trading involves substantial risk
- **Total Loss Possible**: You can lose your entire investment
- **No Guarantees**: Past performance doesn't guarantee future results
- **Professional Advice**: Consult financial advisors before trading
- **Regulatory Compliance**: Ensure compliance with local regulations

### Use at Your Own Risk
- Test thoroughly before live trading
- Understand all risks involved
- Only trade with money you can afford to lose
- Monitor positions actively
- Have proper risk management

---

## 🎯 Quick Reference

### Start Paper Trading
```bash
python run_nifty_options.py
# Choose option 1, 2, or 3 for quick start
```

### Start Live Trading (Advanced Users)
```bash
python nifty_options_enhanced.py
# Choose live trading mode
# Type confirmation when prompted
```

### Modify Configuration
Edit `nifty_options_config.py` and restart strategy.

### Emergency Stop
Press `Ctrl+C` to stop strategy and square off positions.

---

**Trade Responsibly! 📈⚠️**