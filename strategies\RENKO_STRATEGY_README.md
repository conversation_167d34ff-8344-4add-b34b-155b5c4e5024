# Renko SuperTrend Strategy for OpenAlgo

A comprehensive Renko-based trading strategy with SuperTrend filter and dynamic re-entry logic, specifically designed for OpenAlgo with Upstox integration.

## 🎯 Strategy Overview

This strategy implements your exact requirements:

1. **Renko-based entries** with configurable box sizes (4-15 points)
2. **SuperTrend filter** for initial entry signals
3. **Fixed 10-point stop loss** from entry price
4. **Dynamic re-entry logic** - re-enter when price moves 10 points in favorable direction from stop loss
5. **Real-time price monitoring** (1-second tick simulation)
6. **Configurable trade direction** (BUY only, SELL only, or BOTH)

## 📁 Files Included

- `renko_supertrend_strategy.py` - Basic implementation
- `renko_supertrend_enhanced.py` - Advanced version with full features
- `renko_config.py` - Configuration file for easy parameter adjustment
- `run_renko_strategy.py` - Simple launcher with preset modes
- `RENKO_STRATEGY_README.md` - This documentation

## 🚀 Quick Start

### Option 1: Simple Launcher (Recommended for beginners)
```bash
python run_renko_strategy.py
```

### Option 2: Enhanced Version (Full features)
```bash
python renko_supertrend_enhanced.py
```

### Option 3: Basic Version
```bash
python renko_supertrend_strategy.py
```

## ⚙️ Configuration

### Basic Parameters

| Parameter | Description | Default | Range |
|-----------|-------------|---------|-------|
| `RENKO_BOX_SIZE` | Renko brick size in points | 10 | 4-15 |
| `STOP_LOSS_POINTS` | Stop loss distance in points | 10 | 5-50 |
| `QUANTITY` | Trading quantity | 1 | 1+ |
| `TRADE_DIRECTION` | Trading direction | "BOTH" | "BUY", "SELL", "BOTH" |

### SuperTrend Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `SUPERTREND_PERIOD` | ATR period for SuperTrend | 10 |
| `SUPERTREND_MULTIPLIER` | SuperTrend multiplier | 3.0 |

### Symbol Configuration

Edit `renko_config.py` to set symbol-specific parameters:

```python
SYMBOL_CONFIGS = {
    "RELIANCE": {
        "renko_box_size": 10,
        "stop_loss_points": 10,
        "quantity": 1
    },
    "NIFTY": {
        "renko_box_size": 15,
        "stop_loss_points": 15,
        "quantity": 1
    }
}
```

## 🎮 Usage Modes

### 1. Paper Trading Mode (Recommended for testing)
- Simulates all trades without real money
- Perfect for testing strategy logic
- No risk involved

### 2. Live Trading Mode
- Real money trading
- Requires careful testing first
- Use small quantities initially

### 3. Backtest Mode
- Test strategy on historical data
- Analyze performance metrics
- Optimize parameters

## 📊 Strategy Logic

### Initial Entry
1. **SuperTrend Signal**: Wait for SuperTrend to change from bearish to bullish (BUY) or bullish to bearish (SELL)
2. **Direction Filter**: Only trade in configured direction (BUY/SELL/BOTH)
3. **Entry Execution**: Place market order when signal occurs
4. **Stop Loss**: Set stop loss 10 points away from entry

### Re-entry Logic
1. **After Stop Loss Hit**: Store the stop loss price
2. **Price Movement Check**: Monitor if price moves 10 points in favorable direction from last stop loss
3. **Re-entry**: Enter new position when 10-point threshold is crossed
4. **No SuperTrend Dependency**: Re-entries are purely price-based

### Example Scenario
```
Initial price: 100
SuperTrend bullish signal at: 115
Entry: BUY at 115, Stop Loss at 105

Scenario 1 - Loss then Recovery:
Price hits 105 → Exit with loss
Price moves to 115 (105 + 10) → Re-enter BUY at 115, SL at 105

Scenario 2 - Profit then Pullback:
Price runs to 135, then drops to 125 → Exit with profit
Next entry only at 135 (125 + 10) → BUY at 135, SL at 125
```

## 🛡️ Risk Management

### Built-in Safety Features
- **Daily trade limit**: Maximum trades per day
- **Daily loss limit**: Stop trading if daily loss exceeds limit
- **Market hours check**: Only trade during market hours
- **Position size control**: Fixed quantity per trade
- **Paper trading mode**: Test without risk

### Recommended Settings for Beginners
```python
# Conservative settings
RENKO_BOX_SIZE = 5
STOP_LOSS_POINTS = 15
QUANTITY = 1
TRADE_DIRECTION = "BOTH"
PAPER_TRADING = True
```

## 📈 Performance Monitoring

### Real-time Status Display
- Current price and position
- Renko trend and last brick
- P&L (realized and unrealized)
- Trade count and win rate
- SuperTrend status

### Trade History
- All trades are logged with timestamps
- Entry/exit prices and P&L
- Automatic JSON export
- Performance metrics calculation

## 🔧 Advanced Features

### Predefined Configurations
- **Conservative**: Lower risk, stable returns
- **Aggressive**: Higher risk, potentially higher returns
- **Scalping**: Quick trades, small profits

### Custom Indicators
- Real-time Renko brick calculation
- SuperTrend with configurable parameters
- ATR-based volatility measurement

### Data Management
- Efficient memory usage
- Historical data caching
- Real-time price updates

## 📋 Prerequisites

### Required
1. **OpenAlgo installed and running**
2. **Upstox account with API access**
3. **Valid OpenAlgo API key**
4. **Python 3.7+**

### Python Dependencies
```bash
pip install pandas numpy openalgo
```

## 🚨 Important Warnings

### Before Live Trading
1. **Test thoroughly** in paper trading mode
2. **Start with small quantities**
3. **Understand the strategy logic completely**
4. **Monitor initial trades closely**
5. **Have a plan for manual intervention**

### Risk Disclaimers
- **Past performance doesn't guarantee future results**
- **Trading involves risk of loss**
- **Use only risk capital**
- **Monitor positions actively**
- **Have exit strategies ready**

## 🐛 Troubleshooting

### Common Issues

#### "No historical data received"
- Check internet connection
- Verify symbol name and exchange
- Ensure market is open or use recent trading day

#### "Invalid openalgo apikey"
- Verify API key is correct
- Check OpenAlgo server is running
- Ensure Upstox connection is active

#### "Error placing order"
- Check account balance
- Verify symbol is tradeable
- Ensure market hours
- Check position limits

### Debug Mode
Enable detailed logging by setting:
```python
LOG_LEVEL = "DEBUG"
```

## 📞 Support

### Getting Help
1. Check this README first
2. Review the configuration file
3. Test in paper trading mode
4. Check OpenAlgo documentation
5. Verify Upstox API status

### Customization
The strategy is highly customizable. Key areas for modification:
- Entry/exit logic in `should_enter_trade()` and `should_exit_trade()`
- Risk management in `place_order()` and `exit_position()`
- Indicator calculations in `calculate_supertrend()` and `update_renko_bricks()`

## 📊 Performance Metrics

### Tracked Metrics
- **Total P&L**: Cumulative profit/loss
- **Win Rate**: Percentage of profitable trades
- **Max Drawdown**: Largest peak-to-trough decline
- **Average P&L per Trade**: Mean profit/loss per trade
- **Trade Frequency**: Number of trades per day
- **Sharpe Ratio**: Risk-adjusted returns (in enhanced version)

### Export Options
- JSON trade history
- CSV performance data
- Real-time status logs

## 🔄 Updates and Maintenance

### Regular Tasks
1. **Monitor performance** daily
2. **Review trade logs** weekly
3. **Adjust parameters** based on market conditions
4. **Update stop loss levels** if needed
5. **Backup trade history** regularly

### Version History
- **v1.0**: Basic Renko SuperTrend implementation
- **v2.0**: Enhanced version with full features, risk management, and paper trading
- **v2.1**: Added launcher script and improved documentation

## 📜 License and Disclaimer

This strategy is provided for educational purposes. Users are responsible for:
- Testing thoroughly before live trading
- Understanding the risks involved
- Complying with local regulations
- Managing their own risk

**Use at your own risk. No guarantees of profitability.**

---

## 🎯 Quick Reference

### Start Paper Trading
```bash
python run_renko_strategy.py
# Choose option 1 or 2
```

### Start Live Trading (Advanced Users)
```bash
python renko_supertrend_enhanced.py
# Choose live trading mode
# Type 'CONFIRM' when prompted
```

### Run Backtest
```bash
python run_renko_strategy.py
# Choose option 5
# Select date range and configuration
```

### Modify Configuration
Edit `renko_config.py` and restart the strategy.

---

**Happy Trading! 🚀📈**