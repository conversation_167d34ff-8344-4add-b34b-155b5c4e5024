#!/usr/bin/env python3
"""
Configuration file for NIFTY EMA-MACD Options Strategy
=====================================================

Customize these parameters for your trading preferences
"""

# =============================================================================
# STRATEGY CONFIGURATION
# =============================================================================

# EMA Parameters
EMA_FAST = 9        # Fast EMA period
EMA_MEDIUM = 21     # Medium EMA period  
EMA_SLOW = 34       # Slow EMA period

# MACD Parameters
MACD_FAST = 9       # MACD fast EMA
MACD_SLOW = 13      # MACD slow EMA
MACD_SIGNAL = 9     # MACD signal line EMA

# Timeframe Options
AVAILABLE_TIMEFRAMES = ["1m", "2m", "3m", "5m"]
DEFAULT_TIMEFRAME = "1m"

# =============================================================================
# NIFTY OPTIONS CONFIGURATION
# =============================================================================

# Underlying Asset
UNDERLYING_SYMBOL = "NIFTY"
UNDERLYING_EXCHANGE = "NSE"
OPTIONS_EXCHANGE = "NFO"

# NIFTY Specifications
NIFTY_LOT_SIZE = 25         # Current NIFTY lot size
STRIKE_INTERVAL = 50        # NIFTY strike price intervals
DEFAULT_PRODUCT = "MIS"     # Intraday trading

# Strike Selection Preferences
PREFERRED_STRIKE_TYPE = "ITM1"  # ITM1, ATM, OTM1
ITM_LEVELS = {
    "ITM1": 1,  # 1 strike ITM
    "ITM2": 2,  # 2 strikes ITM
    "ATM": 0,   # At the money
    "OTM1": -1, # 1 strike OTM
    "OTM2": -2  # 2 strikes OTM
}

# =============================================================================
# RISK MANAGEMENT CONFIGURATION
# =============================================================================

# Stop Loss Settings
STOP_LOSS_POINTS = 15           # Fixed stop loss in points
SWING_LOOKBACK_PERIODS = 20     # Periods to look back for swing high/low
MIN_STOP_LOSS_PERCENTAGE = 50   # Minimum SL as % of entry price

# Trailing Stop Loss
TRAILING_TRIGGER_POINTS = 10    # Points profit to activate trailing
TRAILING_STEP_POINTS = 5        # Points to trail the stop loss

# Position Sizing
DEFAULT_QUANTITY = 1            # Default number of lots
MAX_QUANTITY = 5               # Maximum allowed lots
MIN_QUANTITY = 1               # Minimum lots

# =============================================================================
# TRADING HOURS & TIMING
# =============================================================================

# Market Hours (IST)
MARKET_START_TIME = "09:15"
MARKET_END_TIME = "15:30"
STRATEGY_START_TIME = "09:30"  # Start 15 mins after market open
STRATEGY_END_TIME = "15:15"    # Stop 15 mins before market close

# Execution Timing
EXECUTION_INTERVAL = 30         # Seconds between strategy iterations
MAX_ERRORS_ALLOWED = 5          # Max consecutive errors before stopping

# =============================================================================
# SIGNAL VALIDATION
# =============================================================================

# EMA Slope Validation
MIN_EMA_SLOPE_PERIODS = 3       # Periods to confirm slope direction
EMA_ALIGNMENT_BUFFER = 0.5      # Buffer for EMA alignment (points)

# MACD Validation
MACD_CROSSOVER_CONFIRMATION = 2 # Bars to confirm crossover
MIN_MACD_HISTOGRAM_VALUE = 0.1  # Minimum histogram value for signal

# Signal Filtering
AVOID_CHOPPY_MARKET = True      # Filter signals in choppy conditions
ATR_CHOPPINESS_THRESHOLD = 20   # ATR threshold for choppy market detection

# =============================================================================
# LOGGING & MONITORING
# =============================================================================

# Logging Configuration
LOG_LEVEL = "INFO"              # DEBUG, INFO, WARNING, ERROR
LOG_FILE = "nifty_ema_macd_strategy.log"
MAX_LOG_SIZE_MB = 10           # Max log file size in MB
LOG_BACKUP_COUNT = 5           # Number of backup log files

# Performance Monitoring
TRACK_PERFORMANCE = True        # Enable performance tracking
SAVE_TRADES_TO_CSV = True      # Save trade history to CSV
PERFORMANCE_REPORT_INTERVAL = 100  # Generate report every N trades

# =============================================================================
# SAFETY FEATURES
# =============================================================================

# Paper Trading
DEFAULT_PAPER_TRADING = True    # Start with paper trading by default
PAPER_TRADING_WARNING = True    # Show warning when switching to live

# Position Limits
MAX_DAILY_TRADES = 10          # Maximum trades per day
MAX_DAILY_LOSS = 1000          # Maximum daily loss in rupees
MAX_POSITION_VALUE = 50000     # Maximum position value in rupees

# Emergency Settings
EMERGENCY_EXIT_LOSS_PCT = 80   # Emergency exit at 80% loss
FORCE_EXIT_TIME = "15:10"      # Force exit all positions by this time

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Trade Notifications
NOTIFY_ON_ENTRY = True         # Notify on position entry
NOTIFY_ON_EXIT = True          # Notify on position exit
NOTIFY_ON_STOP_LOSS = True     # Notify on stop loss hit
NOTIFY_ON_PROFIT_TARGET = True # Notify on profit target hit

# Alert Thresholds
PROFIT_ALERT_PERCENTAGE = 20   # Alert when profit exceeds this %
LOSS_ALERT_PERCENTAGE = 15     # Alert when loss exceeds this %

# =============================================================================
# ADVANCED FEATURES
# =============================================================================

# Multi-timeframe Analysis
ENABLE_MTF_ANALYSIS = False    # Enable multi-timeframe confirmation
MTF_HIGHER_TIMEFRAME = "5m"    # Higher timeframe for confirmation
MTF_TREND_ALIGNMENT = True     # Require trend alignment across timeframes

# Volume Analysis
ENABLE_VOLUME_FILTER = False   # Enable volume-based filtering
MIN_VOLUME_MULTIPLIER = 1.5    # Minimum volume vs average

# Volatility Filters
ENABLE_VOLATILITY_FILTER = False  # Enable volatility-based filtering
MIN_ATR_VALUE = 10             # Minimum ATR for trade entry
MAX_ATR_VALUE = 100            # Maximum ATR for trade entry

# =============================================================================
# BACKTESTING CONFIGURATION
# =============================================================================

# Backtesting Parameters
BACKTEST_START_DATE = "2024-01-01"
BACKTEST_END_DATE = "2024-12-31"
BACKTEST_INITIAL_CAPITAL = 100000
BACKTEST_COMMISSION_PER_TRADE = 20

# Performance Metrics
CALCULATE_SHARPE_RATIO = True
CALCULATE_MAX_DRAWDOWN = True
CALCULATE_WIN_RATE = True
CALCULATE_PROFIT_FACTOR = True

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_strategy_config():
    """Return strategy configuration as dictionary"""
    return {
        'ema': {
            'fast': EMA_FAST,
            'medium': EMA_MEDIUM,
            'slow': EMA_SLOW
        },
        'macd': {
            'fast': MACD_FAST,
            'slow': MACD_SLOW,
            'signal': MACD_SIGNAL
        },
        'risk_management': {
            'stop_loss_points': STOP_LOSS_POINTS,
            'trailing_trigger': TRAILING_TRIGGER_POINTS,
            'swing_lookback': SWING_LOOKBACK_PERIODS
        },
        'trading': {
            'timeframe': DEFAULT_TIMEFRAME,
            'quantity': DEFAULT_QUANTITY,
            'product': DEFAULT_PRODUCT
        }
    }

def validate_config():
    """Validate configuration parameters"""
    errors = []
    
    # Validate EMA periods
    if not (EMA_FAST < EMA_MEDIUM < EMA_SLOW):
        errors.append("EMA periods must be in ascending order: FAST < MEDIUM < SLOW")
    
    # Validate MACD periods
    if MACD_FAST >= MACD_SLOW:
        errors.append("MACD fast period must be less than slow period")
    
    # Validate quantities
    if not (MIN_QUANTITY <= DEFAULT_QUANTITY <= MAX_QUANTITY):
        errors.append("Default quantity must be between MIN and MAX quantity")
    
    # Validate risk parameters
    if STOP_LOSS_POINTS <= 0:
        errors.append("Stop loss points must be positive")
    
    if TRAILING_TRIGGER_POINTS <= 0:
        errors.append("Trailing trigger points must be positive")
    
    return errors

def print_config_summary():
    """Print configuration summary"""
    print("📋 NIFTY EMA-MACD Strategy Configuration")
    print("=" * 50)
    print(f"📊 EMA Periods: {EMA_FAST}/{EMA_MEDIUM}/{EMA_SLOW}")
    print(f"📈 MACD Settings: {MACD_FAST}/{MACD_SLOW}/{MACD_SIGNAL}")
    print(f"⏰ Default Timeframe: {DEFAULT_TIMEFRAME}")
    print(f"💰 Default Quantity: {DEFAULT_QUANTITY} lots")
    print(f"🛑 Stop Loss: {STOP_LOSS_POINTS} points")
    print(f"📈 Trailing Trigger: {TRAILING_TRIGGER_POINTS} points")
    print(f"📝 Paper Trading: {'ON' if DEFAULT_PAPER_TRADING else 'OFF'}")
    print("=" * 50)

if __name__ == "__main__":
    # Validate and print configuration
    errors = validate_config()
    if errors:
        print("❌ Configuration Errors:")
        for error in errors:
            print(f"   • {error}")
    else:
        print("✅ Configuration is valid")
        print_config_summary()
