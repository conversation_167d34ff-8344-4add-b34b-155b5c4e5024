#!/usr/bin/env python3
"""
NIFTY Options EMA-MACD Confluence Strategy
===========================================

Strategy Logic:
- Analyze NIFTY SPOT with EMA(9,21,34) + MACD(9,13)
- Execute ITM/ATM CE/PE options based on confluence signals
- Multi-timeframe support: 1m, 2m, 3m, 5m
- Advanced risk management with swing-based SL and trailing

Author: OpenAlgo Strategy Builder
Version: 1.0
"""

from openalgo import api
import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class NiftyEMAMACDOptionsStrategy:
    """
    Advanced NIFTY Options Strategy using EMA + MACD Confluence
    """
    
    def __init__(self, api_key: str):
        """Initialize the strategy"""
        self.api_key = api_key
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('nifty_ema_macd_strategy.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Strategy Configuration
        self.underlying_symbol = "NIFTY"
        self.underlying_exchange = "NSE"
        self.options_exchange = "NFO"
        self.nifty_lot_size = 25
        self.strike_interval = 50
        
        # EMA Parameters
        self.ema_fast = 9
        self.ema_medium = 21
        self.ema_slow = 34
        
        # MACD Parameters
        self.macd_fast = 9
        self.macd_slow = 13
        self.macd_signal = 9
        
        # Trading Parameters
        self.timeframe = "1m"  # Default timeframe
        self.quantity = 1  # Number of lots
        self.product = "MIS"  # Intraday
        
        # Risk Management
        self.stop_loss_points = 15
        self.trailing_trigger_points = 10
        self.swing_lookback = 20  # Periods to look back for swing high/low
        
        # Position Tracking
        self.current_position = None
        self.entry_price = 0
        self.stop_loss_price = 0
        self.trailing_active = False
        self.swing_high = 0
        self.swing_low = 0
        
        # Strategy State
        self.strategy_name = "NIFTY EMA-MACD Options"
        self.paper_trading = True  # Start with paper trading
        
        self.logger.info("🚀 NIFTY EMA-MACD Options Strategy Initialized")
        self.logger.info(f"📊 EMA: {self.ema_fast}/{self.ema_medium}/{self.ema_slow}")
        self.logger.info(f"📈 MACD: {self.macd_fast}/{self.macd_slow}/{self.macd_signal}")
        self.logger.info(f"⏰ Timeframe: {self.timeframe}")
    
    def set_timeframe(self, timeframe: str):
        """Set trading timeframe"""
        valid_timeframes = ["1m", "2m", "3m", "5m"]
        if timeframe in valid_timeframes:
            self.timeframe = timeframe
            self.logger.info(f"⏰ Timeframe changed to: {timeframe}")
        else:
            self.logger.error(f"❌ Invalid timeframe: {timeframe}. Valid: {valid_timeframes}")
    
    def set_paper_trading(self, enabled: bool):
        """Enable/disable paper trading"""
        self.paper_trading = enabled
        mode = "PAPER" if enabled else "LIVE"
        self.logger.info(f"📝 Trading mode: {mode}")
    
    def get_nifty_data(self, days: int = 7) -> Optional[pd.DataFrame]:
        """Get NIFTY historical data"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.underlying_symbol,
                exchange=self.underlying_exchange,
                interval=self.timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                self.logger.warning("⚠️ No NIFTY data received")
                return None
            
            # Ensure we have enough data for calculations
            min_periods = max(self.ema_slow, self.macd_slow, self.swing_lookback) + 10
            if len(df) < min_periods:
                self.logger.warning(f"⚠️ Insufficient data: {len(df)} < {min_periods}")
                return None
            
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Error fetching NIFTY data: {e}")
            return None
    
    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """Calculate Exponential Moving Average"""
        return data.ewm(span=period, adjust=False).mean()
    
    def calculate_macd(self, data: pd.Series) -> Dict[str, pd.Series]:
        """Calculate MACD with custom periods"""
        ema_fast = self.calculate_ema(data, self.macd_fast)
        ema_slow = self.calculate_ema(data, self.macd_slow)
        
        macd_line = ema_fast - ema_slow
        signal_line = self.calculate_ema(macd_line, self.macd_signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def analyze_ema_slope(self, df: pd.DataFrame) -> Dict[str, bool]:
        """Analyze EMA slopes and alignment"""
        # Calculate EMAs
        df['ema_9'] = self.calculate_ema(df['close'], self.ema_fast)
        df['ema_21'] = self.calculate_ema(df['close'], self.ema_medium)
        df['ema_34'] = self.calculate_ema(df['close'], self.ema_slow)
        
        # Get latest values
        current_9 = df['ema_9'].iloc[-1]
        current_21 = df['ema_21'].iloc[-1]
        current_34 = df['ema_34'].iloc[-1]
        
        # Get previous values for slope calculation
        prev_9 = df['ema_9'].iloc[-3]  # Use -3 to avoid noise
        prev_21 = df['ema_21'].iloc[-3]
        prev_34 = df['ema_34'].iloc[-3]
        
        # Check slopes (upward trending)
        ema_9_rising = current_9 > prev_9
        ema_21_rising = current_21 > prev_21
        ema_34_rising = current_34 > prev_34
        
        # Check alignment for bullish setup: 9 > 21 > 34
        bullish_alignment = current_9 > current_21 > current_34
        bullish_slope = ema_9_rising and ema_21_rising and ema_34_rising
        
        # Check alignment for bearish setup: 9 < 21 < 34
        bearish_alignment = current_9 < current_21 < current_34
        bearish_slope = not ema_9_rising and not ema_21_rising and not ema_34_rising
        
        return {
            'bullish_setup': bullish_alignment and bullish_slope,
            'bearish_setup': bearish_alignment and bearish_slope,
            'ema_9': current_9,
            'ema_21': current_21,
            'ema_34': current_34,
            'slopes': {
                'ema_9_rising': ema_9_rising,
                'ema_21_rising': ema_21_rising,
                'ema_34_rising': ema_34_rising
            }
        }
    
    def analyze_macd_signals(self, df: pd.DataFrame) -> Dict[str, bool]:
        """Analyze MACD crossover signals"""
        macd_data = self.calculate_macd(df['close'])
        
        # Get current and previous values
        current_macd = macd_data['macd'].iloc[-1]
        current_signal = macd_data['signal'].iloc[-1]
        prev_macd = macd_data['macd'].iloc[-2]
        prev_signal = macd_data['signal'].iloc[-2]
        
        # Detect crossovers
        bullish_crossover = (prev_macd <= prev_signal) and (current_macd > current_signal)
        bearish_crossover = (prev_macd >= prev_signal) and (current_macd < current_signal)
        
        return {
            'bullish_crossover': bullish_crossover,
            'bearish_crossover': bearish_crossover,
            'macd': current_macd,
            'signal': current_signal,
            'histogram': macd_data['histogram'].iloc[-1]
        }
    
    def find_swing_levels(self, df: pd.DataFrame) -> Dict[str, float]:
        """Find swing high and low for stop loss calculation"""
        lookback = min(self.swing_lookback, len(df) - 1)
        
        # Get recent high and low data
        recent_highs = df['high'].tail(lookback)
        recent_lows = df['low'].tail(lookback)
        
        swing_high = recent_highs.max()
        swing_low = recent_lows.min()
        
        return {
            'swing_high': swing_high,
            'swing_low': swing_low
        }

    def get_atm_strike(self, nifty_price: float) -> int:
        """Get ATM strike price for NIFTY"""
        atm_strike = round(nifty_price / self.strike_interval) * self.strike_interval
        return int(atm_strike)

    def get_itm_strike(self, nifty_price: float, option_type: str, itm_level: int = 1) -> int:
        """Get ITM strike price"""
        atm_strike = self.get_atm_strike(nifty_price)

        if option_type == "CE":
            # For CE, ITM means strike < spot price
            itm_strike = atm_strike - (itm_level * self.strike_interval)
        else:  # PE
            # For PE, ITM means strike > spot price
            itm_strike = atm_strike + (itm_level * self.strike_interval)

        return int(itm_strike)

    def get_current_expiry(self) -> str:
        """Get current week expiry in OpenAlgo format"""
        today = datetime.now()

        # Find next Thursday (NIFTY weekly expiry)
        days_ahead = 3 - today.weekday()  # Thursday is 3
        if days_ahead <= 0:  # Target day already happened this week
            days_ahead += 7

        expiry_date = today + timedelta(days=days_ahead)
        return expiry_date.strftime("%d%b%y").upper()

    def construct_option_symbol(self, strike: int, option_type: str, expiry: str) -> str:
        """Construct option symbol in OpenAlgo format for UPSTOX"""
        # Format: NIFTY + expiry + strike + option_type
        # Example: NIFTY28NOV2424650CE
        symbol = f"NIFTY{expiry}{strike}{option_type}"
        return symbol

    def get_option_price(self, option_symbol: str) -> Optional[float]:
        """Get current option price"""
        try:
            quote = self.client.quote(symbol=option_symbol, exchange=self.options_exchange)
            if quote and 'ltp' in quote:
                return float(quote['ltp'])
            return None
        except Exception as e:
            self.logger.error(f"❌ Error getting option price for {option_symbol}: {e}")
            return None

    def calculate_stop_loss(self, option_type: str, entry_price: float, swing_levels: Dict[str, float]) -> float:
        """Calculate stop loss based on swing levels and 15-point rule"""
        if option_type == "CE":
            # For CE, use swing low - 15 points, but not less than entry - 15
            swing_based_sl = swing_levels['swing_low'] - self.stop_loss_points
            entry_based_sl = entry_price - self.stop_loss_points
            stop_loss = max(swing_based_sl, entry_based_sl, entry_price * 0.5)  # Min 50% of entry
        else:  # PE
            # For PE, use swing high + 15 points, but not less than entry - 15
            swing_based_sl = swing_levels['swing_high'] + self.stop_loss_points
            entry_based_sl = entry_price - self.stop_loss_points
            stop_loss = max(swing_based_sl, entry_based_sl, entry_price * 0.5)  # Min 50% of entry

        return max(stop_loss, 1.0)  # Minimum SL of 1 rupee

    def should_trail_stop_loss(self, current_price: float) -> bool:
        """Check if trailing stop loss should be activated"""
        if not self.current_position or self.trailing_active:
            return False

        profit = current_price - self.entry_price
        return profit >= self.trailing_trigger_points

    def update_trailing_stop_loss(self, current_price: float, option_type: str):
        """Update trailing stop loss"""
        if not self.trailing_active:
            return

        if option_type == "CE":
            # Trail stop loss upward for CE
            new_sl = current_price - self.stop_loss_points
            if new_sl > self.stop_loss_price:
                self.stop_loss_price = new_sl
                self.logger.info(f"📈 Trailing SL updated to: {self.stop_loss_price:.2f}")
        else:  # PE
            # Trail stop loss upward for PE (same logic as CE for option buying)
            new_sl = current_price - self.stop_loss_points
            if new_sl > self.stop_loss_price:
                self.stop_loss_price = new_sl
                self.logger.info(f"📈 Trailing SL updated to: {self.stop_loss_price:.2f}")

    def place_option_order(self, option_type: str, nifty_price: float, swing_levels: Dict[str, float]) -> bool:
        """Place option order"""
        try:
            # Get strike prices (prefer ITM, fallback to ATM)
            try:
                strike = self.get_itm_strike(nifty_price, option_type, 1)  # 1 ITM
            except:
                strike = self.get_atm_strike(nifty_price)  # Fallback to ATM

            expiry = self.get_current_expiry()
            option_symbol = self.construct_option_symbol(strike, option_type, expiry)

            # Get option price
            option_price = self.get_option_price(option_symbol)
            if not option_price:
                self.logger.error(f"❌ Could not get price for {option_symbol}")
                return False

            # Validate option price (basic sanity check)
            if option_price <= 0 or option_price > nifty_price * 0.1:  # Max 10% of NIFTY price
                self.logger.error(f"❌ Invalid option price: {option_price}")
                return False

            # Calculate stop loss
            stop_loss = self.calculate_stop_loss(option_type, option_price, swing_levels)

            if self.paper_trading:
                self.logger.info(f"📝 PAPER TRADE - {option_type} Order")
                self.logger.info(f"   Symbol: {option_symbol}")
                self.logger.info(f"   Strike: {strike}")
                self.logger.info(f"   Price: {option_price:.2f}")
                self.logger.info(f"   Stop Loss: {stop_loss:.2f}")

                # Update position tracking for paper trading
                self.current_position = {
                    'symbol': option_symbol,
                    'option_type': option_type,
                    'strike': strike,
                    'quantity': self.quantity
                }
                self.entry_price = option_price
                self.stop_loss_price = stop_loss
                self.trailing_active = False

                return True

            # Place actual order
            response = self.client.placesmartorder(
                strategy=self.strategy_name,
                symbol=option_symbol,
                action="BUY",
                exchange=self.options_exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.quantity * self.nifty_lot_size,
                position_size=self.quantity * self.nifty_lot_size
            )

            if response:
                self.logger.info(f"✅ {option_type} Order Placed: {option_symbol}")
                self.logger.info(f"📋 Order Response: {response}")

                # Update position tracking
                self.current_position = {
                    'symbol': option_symbol,
                    'option_type': option_type,
                    'strike': strike,
                    'quantity': self.quantity,
                    'order_id': response.get('orderid')
                }
                self.entry_price = option_price
                self.stop_loss_price = stop_loss
                self.trailing_active = False

                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ Error placing {option_type} order: {e}")
            return False

    def close_position(self, reason: str = "Manual") -> bool:
        """Close current position"""
        if not self.current_position:
            return True

        try:
            if self.paper_trading:
                self.logger.info(f"📝 PAPER TRADE - Position Closed ({reason})")
                self.logger.info(f"   Symbol: {self.current_position['symbol']}")

                # Reset position tracking
                self.current_position = None
                self.entry_price = 0
                self.stop_loss_price = 0
                self.trailing_active = False

                return True

            # Place actual sell order
            response = self.client.placesmartorder(
                strategy=self.strategy_name,
                symbol=self.current_position['symbol'],
                action="SELL",
                exchange=self.options_exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.current_position['quantity'] * self.nifty_lot_size,
                position_size=-(self.current_position['quantity'] * self.nifty_lot_size)
            )

            if response:
                self.logger.info(f"✅ Position Closed ({reason}): {self.current_position['symbol']}")
                self.logger.info(f"📋 Close Response: {response}")

                # Reset position tracking
                self.current_position = None
                self.entry_price = 0
                self.stop_loss_price = 0
                self.trailing_active = False

                return True

            return False

        except Exception as e:
            self.logger.error(f"❌ Error closing position: {e}")
            return False

    def check_exit_conditions(self, current_price: float) -> bool:
        """Check if position should be exited"""
        if not self.current_position:
            return False

        # Check stop loss
        if current_price <= self.stop_loss_price:
            self.logger.info(f"🛑 Stop Loss Hit: {current_price:.2f} <= {self.stop_loss_price:.2f}")
            return self.close_position("Stop Loss")

        # Check trailing stop activation
        if self.should_trail_stop_loss(current_price):
            self.trailing_active = True
            self.logger.info(f"📈 Trailing Stop Activated at price: {current_price:.2f}")

        # Update trailing stop if active
        if self.trailing_active:
            self.update_trailing_stop_loss(current_price, self.current_position['option_type'])

        return False

    def generate_signals(self, df: pd.DataFrame) -> Dict[str, bool]:
        """Generate trading signals based on EMA + MACD confluence"""
        # Analyze EMA setup
        ema_analysis = self.analyze_ema_slope(df)

        # Analyze MACD signals
        macd_analysis = self.analyze_macd_signals(df)

        # Generate confluence signals
        ce_signal = (ema_analysis['bullish_setup'] and
                    macd_analysis['bullish_crossover'])

        pe_signal = (ema_analysis['bearish_setup'] and
                    macd_analysis['bearish_crossover'])

        return {
            'ce_signal': ce_signal,
            'pe_signal': pe_signal,
            'ema_analysis': ema_analysis,
            'macd_analysis': macd_analysis
        }

    def log_strategy_status(self, df: pd.DataFrame, signals: Dict, nifty_price: float):
        """Log current strategy status"""
        ema_analysis = signals['ema_analysis']
        macd_analysis = signals['macd_analysis']

        self.logger.info("\n" + "="*60)
        self.logger.info(f"📊 NIFTY EMA-MACD Strategy Status")
        self.logger.info("="*60)
        self.logger.info(f"💰 NIFTY Price: {nifty_price:.2f}")
        self.logger.info(f"⏰ Timeframe: {self.timeframe}")
        self.logger.info(f"📈 EMA 9: {ema_analysis['ema_9']:.2f}")
        self.logger.info(f"📈 EMA 21: {ema_analysis['ema_21']:.2f}")
        self.logger.info(f"📈 EMA 34: {ema_analysis['ema_34']:.2f}")
        self.logger.info(f"📊 MACD: {macd_analysis['macd']:.4f}")
        self.logger.info(f"📊 Signal: {macd_analysis['signal']:.4f}")
        self.logger.info(f"📊 Histogram: {macd_analysis['histogram']:.4f}")

        # Signal status
        self.logger.info(f"🟢 CE Signal: {'YES' if signals['ce_signal'] else 'NO'}")
        self.logger.info(f"🔴 PE Signal: {'YES' if signals['pe_signal'] else 'NO'}")
        self.logger.info(f"📈 Bullish Setup: {'YES' if ema_analysis['bullish_setup'] else 'NO'}")
        self.logger.info(f"📉 Bearish Setup: {'YES' if ema_analysis['bearish_setup'] else 'NO'}")
        self.logger.info(f"🔄 MACD Bullish Cross: {'YES' if macd_analysis['bullish_crossover'] else 'NO'}")
        self.logger.info(f"🔄 MACD Bearish Cross: {'YES' if macd_analysis['bearish_crossover'] else 'NO'}")

        # Position status
        if self.current_position:
            current_price = self.get_option_price(self.current_position['symbol'])
            if current_price:
                pnl = current_price - self.entry_price
                pnl_pct = (pnl / self.entry_price) * 100

                self.logger.info(f"📍 Current Position: {self.current_position['symbol']}")
                self.logger.info(f"💵 Entry Price: {self.entry_price:.2f}")
                self.logger.info(f"💵 Current Price: {current_price:.2f}")
                self.logger.info(f"💰 P&L: {pnl:.2f} ({pnl_pct:.1f}%)")
                self.logger.info(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
                self.logger.info(f"📈 Trailing: {'ACTIVE' if self.trailing_active else 'INACTIVE'}")
        else:
            self.logger.info(f"📍 Position: NONE")

        self.logger.info("="*60)

    def run_strategy(self):
        """Main strategy execution loop"""
        self.logger.info("🎯 Starting NIFTY EMA-MACD Options Strategy...")
        self.logger.info("Press Ctrl+C to stop")

        # Strategy execution parameters
        sleep_interval = 30  # seconds between iterations
        max_errors = 5
        error_count = 0

        try:
            while True:
                try:
                    # Get NIFTY data
                    df = self.get_nifty_data()
                    if df is None:
                        self.logger.warning("⚠️ No data available, retrying...")
                        time.sleep(sleep_interval)
                        continue

                    # Get current NIFTY price
                    nifty_price = df['close'].iloc[-1]

                    # Generate signals
                    signals = self.generate_signals(df)

                    # Get swing levels for stop loss calculation
                    swing_levels = self.find_swing_levels(df)

                    # Check exit conditions for existing position
                    if self.current_position:
                        current_option_price = self.get_option_price(self.current_position['symbol'])
                        if current_option_price:
                            self.check_exit_conditions(current_option_price)

                    # Entry logic - only if no current position
                    if not self.current_position:
                        if signals['ce_signal']:
                            self.logger.info("🟢 CE ENTRY SIGNAL DETECTED!")
                            self.place_option_order("CE", nifty_price, swing_levels)

                        elif signals['pe_signal']:
                            self.logger.info("🔴 PE ENTRY SIGNAL DETECTED!")
                            self.place_option_order("PE", nifty_price, swing_levels)

                    # Log strategy status
                    self.log_strategy_status(df, signals, nifty_price)

                    # Reset error count on successful iteration
                    error_count = 0

                    # Wait before next iteration
                    self.logger.info(f"⏳ Waiting {sleep_interval} seconds...")
                    time.sleep(sleep_interval)

                except Exception as e:
                    error_count += 1
                    self.logger.error(f"❌ Strategy error ({error_count}/{max_errors}): {e}")

                    if error_count >= max_errors:
                        self.logger.error("❌ Max errors reached, stopping strategy")
                        break

                    time.sleep(sleep_interval)
                    continue

        except KeyboardInterrupt:
            self.logger.info("\n🛑 Strategy stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Fatal strategy error: {e}")
        finally:
            # Close any open positions
            if self.current_position:
                self.close_position("Strategy Stop")
            self.logger.info("🏁 Strategy execution completed")


def main():
    """Main function to run the strategy"""
    print("🎯 NIFTY Options EMA-MACD Confluence Strategy")
    print("=" * 50)
    print("📊 Strategy Features:")
    print("   • EMA(9,21,34) trend analysis")
    print("   • MACD(9,13) confluence signals")
    print("   • ITM/ATM options trading")
    print("   • Multi-timeframe support")
    print("   • Advanced risk management")
    print("   • Swing-based stop loss")
    print("   • Trailing stop loss")
    print("=" * 50)

    # Get API key
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return

    # Initialize strategy
    strategy = NiftyEMAMACDOptionsStrategy(api_key=api_key)

    # Configuration menu
    print("\n📋 Strategy Configuration:")

    # Timeframe selection
    print("\n1. Select Timeframe:")
    print("   1. 1 minute")
    print("   2. 2 minutes")
    print("   3. 3 minutes")
    print("   4. 5 minutes")

    timeframe_choice = input("Select (1-4, default=1): ").strip() or "1"
    timeframe_map = {"1": "1m", "2": "2m", "3": "3m", "4": "5m"}
    if timeframe_choice in timeframe_map:
        strategy.set_timeframe(timeframe_map[timeframe_choice])

    # Trading mode
    print("\n2. Trading Mode:")
    print("   1. Paper Trading (Safe)")
    print("   2. Live Trading")

    mode_choice = input("Select (1-2, default=1): ").strip() or "1"
    strategy.set_paper_trading(mode_choice == "1")

    # Quantity
    try:
        quantity = int(input("\n3. Number of lots (default=1): ").strip() or "1")
        strategy.quantity = max(1, quantity)
    except:
        strategy.quantity = 1

    # Final confirmation
    print(f"\n✅ Strategy Configuration:")
    print(f"   📊 Timeframe: {strategy.timeframe}")
    print(f"   📝 Mode: {'PAPER' if strategy.paper_trading else 'LIVE'}")
    print(f"   💰 Quantity: {strategy.quantity} lots")
    print(f"   🎯 EMA: {strategy.ema_fast}/{strategy.ema_medium}/{strategy.ema_slow}")
    print(f"   📈 MACD: {strategy.macd_fast}/{strategy.macd_slow}")
    print(f"   🛑 Stop Loss: {strategy.stop_loss_points} points")
    print(f"   📈 Trailing: {strategy.trailing_trigger_points} points trigger")

    if not strategy.paper_trading:
        confirm = input("\n⚠️  LIVE TRADING MODE - Are you sure? (yes/no): ").strip().lower()
        if confirm != "yes":
            print("❌ Strategy cancelled")
            return

    input("\nPress Enter to start the strategy...")
    strategy.run_strategy()


if __name__ == "__main__":
    main()
