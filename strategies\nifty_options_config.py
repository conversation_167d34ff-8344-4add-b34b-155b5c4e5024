#!/usr/bin/env python3
"""
Configuration file for NIFTY Options Renko SuperTrend Strategy
Customize these parameters for your NIFTY options trading preferences
"""

# =============================================================================
# NIFTY OPTIONS CONFIGURATION
# =============================================================================

# Underlying Asset
UNDERLYING_SYMBOL = "NIFTY"
UNDERLYING_EXCHANGE = "NSE"
OPTIONS_EXCHANGE = "NFO"

# NIFTY Options Specifications
NIFTY_LOT_SIZE = 25  # Current NIFTY lot size (verify with latest NSE data)
STRIKE_INTERVAL = 50  # NIFTY strike price intervals

# =============================================================================
# OPTIONS STRATEGY PARAMETERS
# =============================================================================

# Option Type Selection
# Options: "CE" (Calls only), "PE" (Puts only), "BOTH" (Both calls and puts)
OPTION_TYPE = "BOTH"

# Strike Selection Strategy
# Options: "ATM", "OTM1", "OTM2", "ITM1", "ITM2"
STRIKE_SELECTION = "ATM"

# Additional strike offset (in points)
STRIKE_OFFSET = 0

# Expiry Preference
# Options: "CURRENT" (current week), "NEXT" (next week), "MONTHLY" (monthly expiry)
EXPIRY_PREFERENCE = "CURRENT"

# Trading Direction
# Options: "CE" (only calls), "PE" (only puts), "BOTH" (both directions)
TRADE_DIRECTION = "BOTH"

# =============================================================================
# RENKO PARAMETERS FOR NIFTY
# =============================================================================

# Renko Box Size (adjusted for NIFTY volatility)
RENKO_BOX_SIZE = 50  # Recommended range: 25-100 for NIFTY

# SuperTrend Parameters
SUPERTREND_PERIOD = 10
SUPERTREND_MULTIPLIER = 3.0

# =============================================================================
# RISK MANAGEMENT FOR OPTIONS
# =============================================================================

# Position Size
QUANTITY = 1  # Number of lots

# Stop Loss Strategy
STOP_LOSS_PERCENTAGE = 50  # Stop loss at 50% of entry price
MAX_LOSS_PER_TRADE = 5000  # Maximum loss per trade in rupees

# Time-based Exits
TIME_DECAY_EXIT_MINUTES = 30  # Exit if no movement in X minutes
AVOID_EXPIRY_DAY = True  # Avoid trading on expiry day

# Greeks-based Exits (if available)
THETA_THRESHOLD = -50  # Exit if theta exceeds this value

# =============================================================================
# ADVANCED OPTIONS SETTINGS
# =============================================================================

# Auto Strike Adjustment
AUTO_STRIKE_ADJUSTMENT = True  # Automatically adjust strikes based on underlying movement

# Minimum Option Price Filter
MIN_OPTION_PRICE = 1.0  # Avoid options below this price
MAX_OPTION_PRICE_RATIO = 0.1  # Max option price as ratio of underlying (10%)

# Volume and Open Interest Filters (if available)
MIN_VOLUME = 100  # Minimum volume for option selection
MIN_OPEN_INTEREST = 1000  # Minimum OI for option selection

# =============================================================================
# MARKET TIMING
# =============================================================================

# Market Hours for Options Trading
MARKET_START_TIME = "09:15"
MARKET_END_TIME = "15:30"

# Avoid trading in first/last X minutes
AVOID_FIRST_MINUTES = 15  # Avoid first 15 minutes
AVOID_LAST_MINUTES = 15   # Avoid last 15 minutes

# =============================================================================
# PREDEFINED STRATEGY CONFIGURATIONS
# =============================================================================

# Conservative Options Strategy
CONSERVATIVE_OPTIONS_CONFIG = {
    "option_type": "BOTH",
    "strike_selection": "ATM",
    "expiry_preference": "CURRENT",
    "renko_box_size": 75,
    "quantity": 1,
    "stop_loss_percentage": 40,
    "time_decay_exit_minutes": 45,
    "avoid_expiry_day": True
}

# Aggressive Options Strategy
AGGRESSIVE_OPTIONS_CONFIG = {
    "option_type": "BOTH",
    "strike_selection": "OTM1",
    "expiry_preference": "CURRENT",
    "renko_box_size": 25,
    "quantity": 2,
    "stop_loss_percentage": 60,
    "time_decay_exit_minutes": 20,
    "avoid_expiry_day": False
}

# Scalping Options Strategy
SCALPING_OPTIONS_CONFIG = {
    "option_type": "BOTH",
    "strike_selection": "ATM",
    "expiry_preference": "CURRENT",
    "renko_box_size": 30,
    "quantity": 3,
    "stop_loss_percentage": 30,
    "time_decay_exit_minutes": 10,
    "avoid_expiry_day": True
}

# Weekly Options Strategy
WEEKLY_OPTIONS_CONFIG = {
    "option_type": "BOTH",
    "strike_selection": "OTM1",
    "expiry_preference": "CURRENT",
    "renko_box_size": 40,
    "quantity": 1,
    "stop_loss_percentage": 50,
    "time_decay_exit_minutes": 60,
    "avoid_expiry_day": True
}

# Monthly Options Strategy
MONTHLY_OPTIONS_CONFIG = {
    "option_type": "BOTH",
    "strike_selection": "ATM",
    "expiry_preference": "MONTHLY",
    "renko_box_size": 60,
    "quantity": 1,
    "stop_loss_percentage": 40,
    "time_decay_exit_minutes": 120,
    "avoid_expiry_day": True
}

# =============================================================================
# STRIKE-SPECIFIC CONFIGURATIONS
# =============================================================================

# Different configurations based on NIFTY levels
NIFTY_LEVEL_CONFIGS = {
    "below_20000": {
        "renko_box_size": 40,
        "strike_interval": 50,
        "volatility_multiplier": 1.2
    },
    "20000_25000": {
        "renko_box_size": 50,
        "strike_interval": 50,
        "volatility_multiplier": 1.0
    },
    "above_25000": {
        "renko_box_size": 60,
        "strike_interval": 50,
        "volatility_multiplier": 0.8
    }
}

# =============================================================================
# EXPIRY-SPECIFIC SETTINGS
# =============================================================================

# Days before expiry to avoid trading
DAYS_BEFORE_EXPIRY_AVOID = 0  # 0 = trade until expiry day

# Expiry day trading settings
EXPIRY_DAY_SETTINGS = {
    "avoid_trading": True,
    "square_off_time": "15:00",  # Square off all positions by this time
    "no_new_positions": True
}

# =============================================================================
# VOLATILITY-BASED ADJUSTMENTS
# =============================================================================

# VIX-based adjustments (if VIX data is available)
VIX_BASED_ADJUSTMENTS = {
    "low_vix": {  # VIX < 15
        "renko_box_size_multiplier": 0.8,
        "stop_loss_multiplier": 0.8
    },
    "medium_vix": {  # VIX 15-25
        "renko_box_size_multiplier": 1.0,
        "stop_loss_multiplier": 1.0
    },
    "high_vix": {  # VIX > 25
        "renko_box_size_multiplier": 1.3,
        "stop_loss_multiplier": 1.2
    }
}

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_config_by_strategy_type(strategy_type: str) -> dict:
    """Get configuration by strategy type"""
    configs = {
        "conservative": CONSERVATIVE_OPTIONS_CONFIG,
        "aggressive": AGGRESSIVE_OPTIONS_CONFIG,
        "scalping": SCALPING_OPTIONS_CONFIG,
        "weekly": WEEKLY_OPTIONS_CONFIG,
        "monthly": MONTHLY_OPTIONS_CONFIG
    }
    return configs.get(strategy_type.lower(), CONSERVATIVE_OPTIONS_CONFIG)

def get_nifty_level_config(nifty_price: float) -> dict:
    """Get configuration based on NIFTY level"""
    if nifty_price < 20000:
        return NIFTY_LEVEL_CONFIGS["below_20000"]
    elif nifty_price <= 25000:
        return NIFTY_LEVEL_CONFIGS["20000_25000"]
    else:
        return NIFTY_LEVEL_CONFIGS["above_25000"]

def adjust_for_volatility(base_config: dict, vix_level: str) -> dict:
    """Adjust configuration based on volatility"""
    if vix_level in VIX_BASED_ADJUSTMENTS:
        adjustments = VIX_BASED_ADJUSTMENTS[vix_level]
        adjusted_config = base_config.copy()
        
        if "renko_box_size" in adjusted_config:
            adjusted_config["renko_box_size"] = int(
                adjusted_config["renko_box_size"] * adjustments["renko_box_size_multiplier"]
            )
        
        return adjusted_config
    
    return base_config

# =============================================================================
# SYMBOL CONSTRUCTION HELPERS
# =============================================================================

def get_option_symbol_format():
    """Return the expected option symbol format"""
    return {
        "format": "NIFTY{expiry}{strike}{option_type}",
        "example": "NIFTY28NOV2424650CE",
        "expiry_format": "DDMMMYY",
        "strike_format": "integer",
        "option_type": "CE or PE"
    }

def validate_option_symbol(symbol: str) -> bool:
    """Validate if option symbol follows correct format"""
    if not symbol.startswith("NIFTY"):
        return False
    
    if not (symbol.endswith("CE") or symbol.endswith("PE")):
        return False
    
    # Additional validation can be added here
    return True

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Logging Configuration
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
LOG_OPTIONS_TRADES = True
LOG_GREEKS = True  # Log Greeks if available
LOG_VOLATILITY = True

# Performance Monitoring
TRACK_METRICS = {
    "win_rate": True,
    "average_holding_time": True,
    "max_drawdown": True,
    "profit_factor": True,
    "sharpe_ratio": True
}

# =============================================================================
# SAFETY LIMITS
# =============================================================================

# Daily Limits
MAX_TRADES_PER_DAY = 20
MAX_DAILY_LOSS = 10000  # Maximum daily loss in rupees
MAX_POSITION_SIZE = 5   # Maximum number of lots

# Weekly/Monthly Limits
MAX_WEEKLY_LOSS = 25000
MAX_MONTHLY_LOSS = 50000

# =============================================================================
# PAPER TRADING SETTINGS
# =============================================================================

# Paper Trading Configuration
PAPER_TRADING_DEFAULT = True  # Start with paper trading by default
PAPER_TRADING_SLIPPAGE = 0.5  # Assume 0.5 point slippage in paper trading
PAPER_TRADING_BROKERAGE = 20  # Assume Rs. 20 per trade brokerage

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Trade Notifications
NOTIFY_ON_ENTRY = True
NOTIFY_ON_EXIT = True
NOTIFY_ON_STOP_LOSS = True
NOTIFY_ON_PROFIT_TARGET = True

# Status Update Frequency
STATUS_UPDATE_INTERVAL = 30  # seconds