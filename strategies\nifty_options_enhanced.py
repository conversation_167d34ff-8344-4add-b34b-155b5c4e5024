#!/usr/bin/env python3
"""
Enhanced NIFTY Options Renko SuperTrend Strategy for OpenAlgo
Author: OpenAlgo Strategy Builder
Version: 2.0

Advanced NIFTY Options trading with:
- Automatic ATM/OTM/ITM option selection
- Dynamic expiry management
- Options-specific risk management
- Real-time Greeks monitoring (if available)
- Volatility-based adjustments
- Multiple strategy presets
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openalgo import api
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any, Tuple, List
import json
import math

# Import configuration
try:
    from nifty_options_config import *
except ImportError:
    # Fallback configuration
    UNDERLYING_SYMBOL = "NIFTY"
    UNDERLYING_EXCHANGE = "NSE"
    OPTIONS_EXCHANGE = "NFO"
    NIFTY_LOT_SIZE = 25
    RENKO_BOX_SIZE = 50
    OPTION_TYPE = "BOTH"
    STRIKE_SELECTION = "ATM"
    QUANTITY = 1
    STOP_LOSS_PERCENTAGE = 50
    PAPER_TRADING_DEFAULT = True

class EnhancedNiftyOptionsStrategy:
    def __init__(self, api_key: str):
        """
        Initialize the Enhanced NIFTY Options Strategy
        
        Args:
            api_key: OpenAlgo API key
        """
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL if 'LOG_LEVEL' in globals() else 'INFO'),
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # API Configuration
        self.api_key = api_key
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Load configuration
        self.underlying_symbol = UNDERLYING_SYMBOL
        self.underlying_exchange = UNDERLYING_EXCHANGE
        self.options_exchange = OPTIONS_EXCHANGE
        self.lot_size = NIFTY_LOT_SIZE
        self.product = "MIS"
        
        # Options Parameters
        self.option_type = OPTION_TYPE
        self.strike_selection = STRIKE_SELECTION
        self.strike_offset = STRIKE_OFFSET if 'STRIKE_OFFSET' in globals() else 0
        self.expiry_preference = EXPIRY_PREFERENCE if 'EXPIRY_PREFERENCE' in globals() else "CURRENT"
        
        # Strategy Parameters
        self.renko_box_size = RENKO_BOX_SIZE
        self.supertrend_period = SUPERTREND_PERIOD if 'SUPERTREND_PERIOD' in globals() else 10
        self.supertrend_multiplier = SUPERTREND_MULTIPLIER if 'SUPERTREND_MULTIPLIER' in globals() else 3.0
        self.stop_loss_percentage = STOP_LOSS_PERCENTAGE if 'STOP_LOSS_PERCENTAGE' in globals() else 50
        self.quantity = QUANTITY
        
        # Risk Management
        self.max_loss_per_trade = MAX_LOSS_PER_TRADE if 'MAX_LOSS_PER_TRADE' in globals() else 5000
        self.time_decay_exit_minutes = TIME_DECAY_EXIT_MINUTES if 'TIME_DECAY_EXIT_MINUTES' in globals() else 30
        self.avoid_expiry_day = AVOID_EXPIRY_DAY if 'AVOID_EXPIRY_DAY' in globals() else True
        self.max_trades_per_day = MAX_TRADES_PER_DAY if 'MAX_TRADES_PER_DAY' in globals() else 20
        self.max_daily_loss = MAX_DAILY_LOSS if 'MAX_DAILY_LOSS' in globals() else 10000
        
        # Trading Configuration
        self.trade_direction = TRADE_DIRECTION if 'TRADE_DIRECTION' in globals() else "BOTH"
        self.auto_strike_adjustment = AUTO_STRIKE_ADJUSTMENT if 'AUTO_STRIKE_ADJUSTMENT' in globals() else True
        self.paper_trading = PAPER_TRADING_DEFAULT if 'PAPER_TRADING_DEFAULT' in globals() else True
        
        # Price Filters
        self.min_option_price = MIN_OPTION_PRICE if 'MIN_OPTION_PRICE' in globals() else 1.0
        self.max_option_price_ratio = MAX_OPTION_PRICE_RATIO if 'MAX_OPTION_PRICE_RATIO' in globals() else 0.1
        
        # State Variables
        self.position = 0
        self.current_option_symbol = None
        self.current_strike = None
        self.current_expiry = None
        self.current_option_type = None
        self.entry_price = 0.0
        self.stop_loss_price = 0.0
        self.last_stop_loss_price = 0.0
        self.entry_time = None
        self.underlying_price = 0.0
        self.supertrend_signal_used = False
        self.trade_count = 0
        self.daily_trades = 0
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_pnl = 0.0
        
        # Renko State
        self.renko_bricks = []
        self.current_brick_open = None
        self.current_brick_close = None
        self.renko_trend = None
        
        # Data Management
        self.running = False
        self.start_time = datetime.now()
        self.trade_history = []
        
        # Options Data
        self.current_atm_strike = None
        self.available_strikes = []
        self.current_expiry_date = None
        
        self.logger.info(f"🚀 Enhanced NIFTY Options Strategy Initialized")
        self.logger.info(f"📊 Underlying: {self.underlying_symbol}")
        self.logger.info(f"🎯 Option Type: {self.option_type}")
        self.logger.info(f"🎯 Strike Selection: {self.strike_selection}")
        self.logger.info(f"📅 Expiry Preference: {self.expiry_preference}")
        self.logger.info(f"🧱 Renko Box Size: {self.renko_box_size}")
        self.logger.info(f"💰 Lot Size: {self.lot_size}")
        self.logger.info(f"📈 Quantity: {self.quantity} lots ({self.quantity * self.lot_size} shares)")
        self.logger.info(f"🛑 Stop Loss: {self.stop_loss_percentage}% of entry")
        self.logger.info(f"📝 Paper Trading: {'Enabled' if self.paper_trading else 'Disabled'}")

    def configure_strategy(self, strategy_type: str = None, **kwargs):
        """
        Configure strategy with predefined or custom parameters
        
        Args:
            strategy_type: "conservative", "aggressive", "scalping", "weekly", "monthly"
            **kwargs: Custom parameters to override
        """
        # Load predefined configuration
        if strategy_type and 'get_config_by_strategy_type' in globals():
            config = get_config_by_strategy_type(strategy_type)
            for key, value in config.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    self.logger.info(f"📝 Applied {strategy_type} config - {key}: {value}")
        
        # Apply custom parameters
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                self.logger.info(f"📝 Updated {key}: {value}")
            else:
                self.logger.warning(f"⚠️ Unknown parameter: {key}")

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            current_time = datetime.now().time()
            market_start = datetime.strptime(MARKET_START_TIME, '%H:%M').time()
            market_end = datetime.strptime(MARKET_END_TIME, '%H:%M').time()
            
            # Check if within market hours
            if not (market_start <= current_time <= market_end):
                return False
            
            # Check avoid first/last minutes
            if 'AVOID_FIRST_MINUTES' in globals():
                first_avoid_time = (datetime.combine(datetime.today(), market_start) + 
                                  timedelta(minutes=AVOID_FIRST_MINUTES)).time()
                if current_time < first_avoid_time:
                    return False
            
            if 'AVOID_LAST_MINUTES' in globals():
                last_avoid_time = (datetime.combine(datetime.today(), market_end) - 
                                 timedelta(minutes=AVOID_LAST_MINUTES)).time()
                if current_time > last_avoid_time:
                    return False
            
            return True
            
        except:
            # Fallback to basic market hours
            current_time = datetime.now().time()
            market_start = datetime.strptime('09:30', '%H:%M').time()
            market_end = datetime.strptime('15:15', '%H:%M').time()
            return market_start <= current_time <= market_end

    def is_expiry_day(self) -> bool:
        """Check if today is expiry day"""
        if not self.avoid_expiry_day:
            return False
            
        today = datetime.now()
        # Check if today is Thursday (weekly expiry)
        if today.weekday() == 3:  # Thursday
            return True
        
        # Check if it's monthly expiry (last Thursday of month)
        # This is a simplified check
        if today.day > 25 and today.weekday() == 3:
            return True
            
        return False

    def get_nifty_price(self) -> Optional[float]:
        """Get current NIFTY index price"""
        try:
            response = self.client.quotes(
                symbol=self.underlying_symbol,
                exchange=self.underlying_exchange
            )
            
            if response and 'data' in response:
                price = float(response['data'].get('ltp', 0))
                self.underlying_price = price
                return price
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting NIFTY price: {e}")
            return None

    def get_historical_data(self, days: int = 7) -> Optional[pd.DataFrame]:
        """Get historical data for NIFTY index"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.underlying_symbol,
                exchange=self.underlying_exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                self.logger.warning("⚠️ No historical data received for NIFTY")
                return None
                
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Error getting NIFTY historical data: {e}")
            return None

    def calculate_supertrend(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate SuperTrend indicator for NIFTY"""
        if len(df) < self.supertrend_period:
            return df
            
        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR
        price_diffs = [high - low, 
                      high - close.shift(), 
                      close.shift() - low]
        true_range = pd.concat(price_diffs, axis=1)
        true_range = true_range.abs().max(axis=1)
        atr = true_range.ewm(alpha=1/self.supertrend_period, min_periods=self.supertrend_period).mean()

        hl2 = (high + low) / 2
        final_upperband = upperband = hl2 + (self.supertrend_multiplier * atr)
        final_lowerband = lowerband = hl2 - (self.supertrend_multiplier * atr)

        # Initialize supertrend
        supertrend = [True] * len(df)

        for i in range(1, len(df.index)):
            curr, prev = i, i - 1

            if close.iloc[curr] > final_upperband.iloc[prev]:
                supertrend[curr] = True
            elif close.iloc[curr] < final_lowerband.iloc[prev]:
                supertrend[curr] = False
            else:
                supertrend[curr] = supertrend[prev]

                if supertrend[curr] == True and final_lowerband.iloc[curr] < final_lowerband.iloc[prev]:
                    final_lowerband.iat[curr] = final_lowerband.iat[prev]
                if supertrend[curr] == False and final_upperband.iloc[curr] > final_upperband.iloc[prev]:
                    final_upperband.iat[curr] = final_upperband.iat[prev]

            if supertrend[curr] == True:
                final_upperband.iat[curr] = np.nan
            else:
                final_lowerband.iat[curr] = np.nan

        df['supertrend'] = supertrend
        df['supertrend_upper'] = final_upperband
        df['supertrend_lower'] = final_lowerband
        
        return df

    def update_renko_bricks(self, price: float) -> bool:
        """Update Renko bricks with NIFTY price"""
        if self.current_brick_open is None:
            self.current_brick_open = price
            self.current_brick_close = price
            self.renko_trend = None
            return False

        brick_formed = False
        
        # Check for up brick formation
        if price >= self.current_brick_close + self.renko_box_size:
            num_bricks = int((price - self.current_brick_close) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'up',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'up'
            brick_formed = True
            
        # Check for down brick formation
        elif price <= self.current_brick_close - self.renko_box_size:
            num_bricks = int((self.current_brick_close - price) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'down',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'down'
            brick_formed = True

        # Keep only recent bricks
        if len(self.renko_bricks) > 100:
            self.renko_bricks = self.renko_bricks[-100:]

        return brick_formed

    def get_atm_strike(self, nifty_price: float) -> int:
        """Get ATM strike price for NIFTY"""
        strike_interval = STRIKE_INTERVAL if 'STRIKE_INTERVAL' in globals() else 50
        atm_strike = round(nifty_price / strike_interval) * strike_interval
        self.current_atm_strike = int(atm_strike)
        return int(atm_strike)

    def get_target_strike(self, nifty_price: float, option_type: str) -> int:
        """Get target strike based on selection criteria"""
        atm_strike = self.get_atm_strike(nifty_price)
        strike_interval = STRIKE_INTERVAL if 'STRIKE_INTERVAL' in globals() else 50
        
        if self.strike_selection == "ATM":
            target_strike = atm_strike
        elif self.strike_selection == "OTM1":
            target_strike = atm_strike + strike_interval if option_type == "CE" else atm_strike - strike_interval
        elif self.strike_selection == "OTM2":
            target_strike = atm_strike + (2 * strike_interval) if option_type == "CE" else atm_strike - (2 * strike_interval)
        elif self.strike_selection == "ITM1":
            target_strike = atm_strike - strike_interval if option_type == "CE" else atm_strike + strike_interval
        elif self.strike_selection == "ITM2":
            target_strike = atm_strike - (2 * strike_interval) if option_type == "CE" else atm_strike + (2 * strike_interval)
        else:
            target_strike = atm_strike
            
        # Apply additional offset
        target_strike += self.strike_offset
        
        return int(target_strike)

    def get_current_expiry(self) -> str:
        """Get current expiry date in the required format"""
        today = datetime.now()
        
        if self.expiry_preference == "CURRENT":
            # Find next Thursday (weekly expiry)
            days_ahead = 3 - today.weekday()  # Thursday is 3
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            expiry_date = today + timedelta(days=days_ahead)
        elif self.expiry_preference == "NEXT":
            # Next week's expiry
            days_ahead = 3 - today.weekday() + 7
            expiry_date = today + timedelta(days=days_ahead)
        else:  # MONTHLY
            # Last Thursday of current month (simplified)
            if today.day > 25:  # If we're past monthly expiry, get next month
                next_month = today.replace(day=28) + timedelta(days=4)
                expiry_date = next_month - timedelta(days=next_month.weekday() - 3)
            else:
                # Find last Thursday of current month
                last_day = today.replace(day=28) + timedelta(days=4)
                last_day = last_day - timedelta(days=last_day.day)
                expiry_date = last_day - timedelta(days=(last_day.weekday() - 3) % 7)
        
        self.current_expiry_date = expiry_date
        return expiry_date.strftime("%d%b%y").upper()

    def construct_option_symbol(self, strike: int, option_type: str, expiry: str) -> str:
        """
        Construct option symbol in OpenAlgo format for Upstox
        Based on the reformat_symbol function in master_contract_db.py
        
        Original Upstox format: "NIFTY 24650 CE 28 NOV 24"
        OpenAlgo reformatted: "NIFTY28NOV2424650CE"
        """
        symbol = f"NIFTY{expiry}{strike}{option_type}"
        return symbol

    def get_option_price(self, option_symbol: str) -> Optional[float]:
        """Get current option price"""
        try:
            response = self.client.quotes(
                symbol=option_symbol,
                exchange=self.options_exchange
            )
            
            if response and 'data' in response:
                price = float(response['data'].get('ltp', 0))
                return price if price > 0 else None
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting option price for {option_symbol}: {e}")
            return None

    def validate_option_price(self, option_price: float, nifty_price: float) -> bool:
        """Validate if option price is reasonable"""
        if option_price < self.min_option_price:
            self.logger.warning(f"⚠️ Option price {option_price} below minimum {self.min_option_price}")
            return False
        
        max_price = nifty_price * self.max_option_price_ratio
        if option_price > max_price:
            self.logger.warning(f"⚠️ Option price {option_price} above maximum {max_price}")
            return False
        
        return True

    def get_supertrend_signal(self, df: pd.DataFrame) -> Optional[str]:
        """Get SuperTrend signal for NIFTY"""
        if len(df) < 3:
            return None
            
        current_supertrend = df['supertrend'].iloc[-1]
        previous_supertrend = df['supertrend'].iloc[-2]
        
        if current_supertrend and not previous_supertrend:
            return "CE"  # Bullish - buy calls
        elif not current_supertrend and previous_supertrend:
            return "PE"  # Bearish - buy puts
            
        return None

    def should_enter_trade(self, nifty_price: float, df: pd.DataFrame) -> Optional[str]:
        """Determine if we should enter an options trade"""
        # Risk management checks
        if self.daily_trades >= self.max_trades_per_day:
            self.logger.warning(f"⚠️ Daily trade limit reached: {self.daily_trades}")
            return None
            
        if self.daily_pnl <= -self.max_daily_loss:
            self.logger.warning(f"⚠️ Daily loss limit reached: {self.daily_pnl}")
            return None
        
        # Check if expiry day
        if self.is_expiry_day():
            self.logger.warning("⚠️ Avoiding trades on expiry day")
            return None
        
        # Initial entry with SuperTrend
        if self.position == 0 and not self.supertrend_signal_used:
            supertrend_signal = self.get_supertrend_signal(df)
            if supertrend_signal:
                if self.trade_direction == "BOTH" or self.trade_direction == supertrend_signal:
                    return supertrend_signal
                    
        # Re-entry logic based on underlying price movement
        elif self.position == 0 and self.last_stop_loss_price > 0:
            if self.trade_direction in ["CE", "BOTH"]:
                if nifty_price >= self.underlying_price + self.renko_box_size:
                    return "CE"
                    
            if self.trade_direction in ["PE", "BOTH"]:
                if nifty_price <= self.underlying_price - self.renko_box_size:
                    return "PE"
                    
        return None

    def should_exit_trade(self, option_price: float) -> Tuple[bool, str]:
        """Check if we should exit current position"""
        if self.position == 0:
            return False, ""
            
        # Stop loss check
        if option_price <= self.stop_loss_price:
            return True, "Stop Loss Hit"
            
        # Time decay check
        if self.entry_time and self.time_decay_exit_minutes > 0:
            time_elapsed = (datetime.now() - self.entry_time).total_seconds() / 60
            if time_elapsed > self.time_decay_exit_minutes:
                # Check if option has lost significant value
                loss_percentage = ((self.entry_price - option_price) / self.entry_price) * 100
                if loss_percentage > 20:  # 20% loss due to time decay
                    return True, "Time Decay Exit"
        
        # Maximum loss check
        current_loss = (self.entry_price - option_price) * self.position
        if current_loss >= self.max_loss_per_trade:
            return True, "Max Loss Hit"
        
        # Expiry day square off
        if self.is_expiry_day():
            current_time = datetime.now().time()
            square_off_time = datetime.strptime("15:00", "%H:%M").time()
            if current_time >= square_off_time:
                return True, "Expiry Day Square Off"
            
        return False, ""

    def place_option_order(self, option_type: str, nifty_price: float) -> bool:
        """Place options order"""
        try:
            # Get target strike and expiry
            strike = self.get_target_strike(nifty_price, option_type)
            expiry = self.get_current_expiry()
            option_symbol = self.construct_option_symbol(strike, option_type, expiry)
            
            # Get option price
            option_price = self.get_option_price(option_symbol)
            if not option_price:
                self.logger.error(f"❌ Could not get option price for {option_symbol}")
                return False
            
            # Validate option price
            if not self.validate_option_price(option_price, nifty_price):
                return False
            
            # Calculate position size
            position_size = self.quantity * self.lot_size
            
            if self.paper_trading:
                # Simulate order execution
                self.logger.info(f"📋 PAPER TRADE: {option_type} option order simulated")
                response = {"status": "success", "message": "Paper trade executed"}
            else:
                # Place real order
                response = self.client.placesmartorder(
                    strategy="Enhanced NIFTY Options Renko",
                    symbol=option_symbol,
                    action="BUY",
                    exchange=self.options_exchange,
                    price_type="MARKET",
                    product=self.product,
                    quantity=position_size,
                    position_size=position_size
                )
            
            self.logger.info(f"📋 Option Order Response: {response}")
            
            # Update position
            self.position = position_size
            self.current_option_symbol = option_symbol
            self.current_strike = strike
            self.current_expiry = expiry
            self.current_option_type = option_type
            self.entry_price = option_price
            self.stop_loss_price = option_price * (1 - self.stop_loss_percentage / 100)
            self.entry_time = datetime.now()
            self.trade_count += 1
            self.daily_trades += 1
            
            # Record trade entry
            trade_record = {
                'timestamp': datetime.now(),
                'action': 'ENTRY',
                'symbol': option_symbol,
                'option_type': option_type,
                'strike': strike,
                'expiry': expiry,
                'entry_price': option_price,
                'quantity': position_size,
                'underlying_price': nifty_price,
                'stop_loss': self.stop_loss_price
            }
            self.trade_history.append(trade_record)
            
            self.logger.info(f"✅ {option_type} Option Order Placed")
            self.logger.info(f"📊 Symbol: {option_symbol}")
            self.logger.info(f"🎯 Strike: {strike}, Entry: {option_price:.2f}")
            self.logger.info(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            self.logger.info(f"📈 Underlying: {nifty_price:.2f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error placing option order: {e}")
            return False

    def exit_option_position(self, reason: str = "Stop Loss") -> bool:
        """Exit current options position"""
        if self.position == 0 or not self.current_option_symbol:
            return False
            
        try:
            # Get current option price
            current_price = self.get_option_price(self.current_option_symbol)
            if not current_price:
                self.logger.error("❌ Could not get current option price for exit")
                current_price = self.stop_loss_price  # Use stop loss price as fallback
            
            if self.paper_trading:
                # Simulate exit
                self.logger.info(f"📋 PAPER TRADE: Option exit simulated")
                response = {"status": "success", "message": "Paper exit executed"}
            else:
                # Place real exit order
                response = self.client.placesmartorder(
                    strategy="Enhanced NIFTY Options Renko",
                    symbol=self.current_option_symbol,
                    action="SELL",
                    exchange=self.options_exchange,
                    price_type="MARKET",
                    product=self.product,
                    quantity=self.position,
                    position_size=0
                )
            
            # Calculate P&L
            pnl = (current_price - self.entry_price) * self.position
            self.total_pnl += pnl
            self.daily_pnl += pnl
            
            # Update drawdown
            if self.total_pnl > self.peak_pnl:
                self.peak_pnl = self.total_pnl
            drawdown = self.peak_pnl - self.total_pnl
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
            
            # Record trade exit
            holding_time = (datetime.now() - self.entry_time).total_seconds() / 60 if self.entry_time else 0
            trade_record = {
                'timestamp': datetime.now(),
                'action': 'EXIT',
                'symbol': self.current_option_symbol,
                'option_type': self.current_option_type,
                'strike': self.current_strike,
                'exit_price': current_price,
                'quantity': self.position,
                'pnl': pnl,
                'reason': reason,
                'holding_time': holding_time
            }
            self.trade_history.append(trade_record)
            
            self.logger.info(f"🔄 Option Position Exited - {reason}")
            self.logger.info(f"💰 Trade P&L: {pnl:.2f}, Total P&L: {self.total_pnl:.2f}")
            
            # Store last stop loss price for re-entry logic
            self.last_stop_loss_price = self.stop_loss_price
            
            # Reset position
            self.position = 0
            self.current_option_symbol = None
            self.current_strike = None
            self.current_expiry = None
            self.current_option_type = None
            self.entry_price = 0.0
            self.stop_loss_price = 0.0
            self.entry_time = None
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error exiting option position: {e}")
            return False

    def print_detailed_status(self, nifty_price: float, df: pd.DataFrame = None):
        """Print comprehensive options strategy status"""
        print(f"\n{'='*90}")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 NIFTY Price: {nifty_price:.2f} (ATM Strike: {self.get_atm_strike(nifty_price)})")
        print(f"🧱 Renko Trend: {self.renko_trend or 'None'}")
        print(f"📈 Total Trades: {self.trade_count} (Daily: {self.daily_trades})")
        print(f"💵 Total P&L: {self.total_pnl:.2f} (Daily: {self.daily_pnl:.2f})")
        print(f"📉 Max Drawdown: {self.max_drawdown:.2f}")
        
        if self.position != 0:
            current_price = self.get_option_price(self.current_option_symbol)
            print(f"🎯 Current Position: {self.current_option_symbol}")
            print(f"🎯 Strike: {self.current_strike} {self.current_option_type}")
            print(f"📅 Expiry: {self.current_expiry}")
            print(f"💰 Entry Price: {self.entry_price:.2f}")
            print(f"💰 Current Price: {current_price:.2f}" if current_price else "💰 Current Price: N/A")
            print(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            
            if current_price:
                unrealized_pnl = (current_price - self.entry_price) * self.position
                print(f"📊 Unrealized P&L: {unrealized_pnl:.2f}")
                
            if self.entry_time:
                holding_time = (datetime.now() - self.entry_time).total_seconds() / 60
                print(f"⏱️ Holding Time: {holding_time:.1f} minutes")
            
        if df is not None and len(df) > 0:
            supertrend_status = "Bullish (CE)" if df['supertrend'].iloc[-1] else "Bearish (PE)"
            print(f"📈 SuperTrend: {supertrend_status}")
            
        if len(self.renko_bricks) > 0:
            last_brick = self.renko_bricks[-1]
            print(f"🧱 Last Brick: {last_brick['trend'].upper()} ({last_brick['open']:.2f} -> {last_brick['close']:.2f})")
            
        print(f"📅 Current Expiry: {self.get_current_expiry()}")
        print(f"🎯 Strategy Config: {self.strike_selection} {self.option_type}")
        print(f"📝 Paper Trading: {'Yes' if self.paper_trading else 'No'}")
        print(f"⚠️ Expiry Day: {'Yes' if self.is_expiry_day() else 'No'}")
        print(f"{'='*90}")

    def save_trade_history(self, filename: str = None):
        """Save trade history to JSON file"""
        if not filename:
            filename = f"nifty_options_trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            # Convert datetime objects to strings for JSON serialization
            history_for_json = []
            for trade in self.trade_history:
                trade_copy = trade.copy()
                trade_copy['timestamp'] = trade_copy['timestamp'].isoformat()
                history_for_json.append(trade_copy)
            
            with open(filename, 'w') as f:
                json.dump({
                    'strategy': 'Enhanced NIFTY Options Renko',
                    'underlying': self.underlying_symbol,
                    'configuration': {
                        'option_type': self.option_type,
                        'strike_selection': self.strike_selection,
                        'expiry_preference': self.expiry_preference,
                        'renko_box_size': self.renko_box_size,
                        'stop_loss_percentage': self.stop_loss_percentage,
                        'quantity': self.quantity,
                        'lot_size': self.lot_size
                    },
                    'performance': {
                        'total_trades': self.trade_count,
                        'total_pnl': self.total_pnl,
                        'max_drawdown': self.max_drawdown,
                        'daily_pnl': self.daily_pnl,
                        'paper_trading': self.paper_trading
                    },
                    'trades': history_for_json
                }, f, indent=2)
            
            self.logger.info(f"💾 Trade history saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving trade history: {e}")

    def run_strategy(self):
        """Main strategy execution loop"""
        self.logger.info("🎯 Starting Enhanced NIFTY Options Renko Strategy...")
        self.logger.info("Press Ctrl+C to stop")
        
        self.running = True
        last_status_time = 0
        
        try:
            while self.running:
                # Check market hours
                if not self.is_market_open():
                    if self.position != 0:
                        self.exit_option_position("Market Closed")
                    self.logger.info("🕐 Market is closed. Waiting...")
                    time.sleep(60)
                    continue
                
                # Get NIFTY price
                nifty_price = self.get_nifty_price()
                if nifty_price is None:
                    time.sleep(5)
                    continue
                
                # Get historical data for SuperTrend
                df = self.get_historical_data()
                if df is None:
                    time.sleep(5)
                    continue
                
                # Calculate SuperTrend
                df = self.calculate_supertrend(df)
                
                # Update Renko bricks
                brick_formed = self.update_renko_bricks(nifty_price)
                if brick_formed:
                    self.logger.info(f"🧱 New Renko brick formed: {self.renko_trend}")
                
                # Check for exit conditions first
                if self.position != 0:
                    current_option_price = self.get_option_price(self.current_option_symbol)
                    if current_option_price:
                        should_exit, exit_reason = self.should_exit_trade(current_option_price)
                        if should_exit:
                            self.exit_option_position(exit_reason)
                
                # Check for entry conditions
                entry_signal = self.should_enter_trade(nifty_price, df)
                if entry_signal:
                    if self.place_option_order(entry_signal, nifty_price):
                        if not self.supertrend_signal_used:
                            self.supertrend_signal_used = True
                            self.logger.info("🎯 SuperTrend signal used for initial entry")
                
                # Print status periodically
                current_time = time.time()
                status_interval = STATUS_UPDATE_INTERVAL if 'STATUS_UPDATE_INTERVAL' in globals() else 30
                if current_time - last_status_time >= status_interval:
                    self.print_detailed_status(nifty_price, df)
                    last_status_time = current_time
                
                # Wait before next iteration
                time.sleep(1)  # 1-second updates
                
        except KeyboardInterrupt:
            self.logger.info("\n🛑 Strategy stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Strategy error: {e}")
        finally:
            # Square off any open positions
            if self.position != 0:
                self.exit_option_position("Strategy Stopped")
            
            # Save trade history
            self.save_trade_history()
            
            self.running = False
            self.logger.info("🏁 Strategy execution completed")
            self.print_final_summary()

    def print_final_summary(self):
        """Print final performance summary"""
        print(f"\n{'='*90}")
        print(f"📊 ENHANCED NIFTY OPTIONS STRATEGY - FINAL SUMMARY")
        print(f"{'='*90}")
        print(f"📈 Total Trades: {self.trade_count}")
        print(f"💰 Total P&L: {self.total_pnl:.2f}")
        print(f"📉 Max Drawdown: {self.max_drawdown:.2f}")
        print(f"🧱 Total Renko Bricks: {len(self.renko_bricks)}")
        print(f"⏱️ Total Runtime: {str(datetime.now() - self.start_time).split('.')[0]}")
        
        if self.trade_count > 0:
            avg_pnl = self.total_pnl / self.trade_count
            print(f"📊 Average P&L per Trade: {avg_pnl:.2f}")
            
            # Calculate win rate from completed trades
            completed_trades = [t for t in self.trade_history if t.get('action') == 'EXIT']
            if completed_trades:
                winning_trades = sum(1 for trade in completed_trades if trade.get('pnl', 0) > 0)
                win_rate = (winning_trades / len(completed_trades)) * 100
                print(f"🎯 Win Rate: {win_rate:.1f}%")
                
                # Average holding time
                avg_holding_time = sum(trade.get('holding_time', 0) for trade in completed_trades) / len(completed_trades)
                print(f"⏱️ Average Holding Time: {avg_holding_time:.1f} minutes")
        
        print(f"📝 Paper Trading: {'Yes' if self.paper_trading else 'No'}")
        print(f"🎯 Configuration: {self.strike_selection} {self.option_type} {self.expiry_preference}")
        print(f"{'='*90}")

def main():
    """Main function for Enhanced NIFTY Options strategy"""
    print("🎯 Enhanced NIFTY Options Renko SuperTrend Strategy")
    print("=" * 60)
    
    # Get API key
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    # Initialize strategy
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    
    # Configuration menu
    print("\n📝 Strategy Configuration Options:")
    print("1. Use default configuration")
    print("2. Use predefined strategy (conservative/aggressive/scalping/weekly/monthly)")
    print("3. Custom configuration")
    
    config_choice = input("Enter choice (1-3): ").strip()
    
    if config_choice == "2":
        print("\nPredefined Strategies:")
        print("- conservative: Lower risk, ATM options, wider stops")
        print("- aggressive: Higher risk, OTM options, tighter stops")
        print("- scalping: Quick trades, ATM options, very tight stops")
        print("- weekly: Current week expiry focus")
        print("- monthly: Monthly expiry focus")
        
        strategy_type = input("Enter strategy type: ").strip().lower()
        strategy.configure_strategy(strategy_type)
        
    elif config_choice == "3":
        print("\nCustom Configuration:")
        
        # Option type
        print("\nOption Type:")
        print("1. CE (Calls only)")
        print("2. PE (Puts only)")
        print("3. BOTH (Calls and Puts)")
        
        option_choice = input("Enter choice (1-3): ").strip()
        option_map = {"1": "CE", "2": "PE", "3": "BOTH"}
        if option_choice in option_map:
            strategy.configure_strategy(
                option_type=option_map[option_choice],
                trade_direction=option_map[option_choice]
            )
        
        # Strike selection
        print("\nStrike Selection:")
        print("1. ATM")
        print("2. OTM1")
        print("3. OTM2")
        print("4. ITM1")
        
        strike_choice = input("Enter choice (1-4): ").strip()
        strike_map = {"1": "ATM", "2": "OTM1", "3": "OTM2", "4": "ITM1"}
        if strike_choice in strike_map:
            strategy.configure_strategy(strike_selection=strike_map[strike_choice])
        
        # Expiry preference
        print("\nExpiry Preference:")
        print("1. Current week")
        print("2. Next week")
        print("3. Monthly")
        
        expiry_choice = input("Enter choice (1-3): ").strip()
        expiry_map = {"1": "CURRENT", "2": "NEXT", "3": "MONTHLY"}
        if expiry_choice in expiry_map:
            strategy.configure_strategy(expiry_preference=expiry_map[expiry_choice])
        
        # Other parameters
        quantity = input(f"Number of lots (default: {strategy.quantity}): ").strip()
        if quantity and quantity.isdigit():
            strategy.configure_strategy(quantity=int(quantity))
        
        box_size = input(f"Renko box size (25-100, default: {strategy.renko_box_size}): ").strip()
        if box_size and box_size.isdigit() and 25 <= int(box_size) <= 100:
            strategy.configure_strategy(renko_box_size=int(box_size))
    
    # Paper trading option
    paper_trading = input(f"\nEnable paper trading? (y/n, default: {'y' if strategy.paper_trading else 'n'}): ").strip().lower()
    if paper_trading == 'y':
        strategy.configure_strategy(paper_trading=True)
    elif paper_trading == 'n':
        strategy.configure_strategy(paper_trading=False)
    
    # Final confirmation
    print(f"\n✅ Final Strategy Configuration:")
    print(f"📊 Option Type: {strategy.option_type}")
    print(f"🎯 Strike Selection: {strategy.strike_selection}")
    print(f"📅 Expiry: {strategy.expiry_preference}")
    print(f"💰 Quantity: {strategy.quantity} lots ({strategy.quantity * strategy.lot_size} shares)")
    print(f"🧱 Renko Box Size: {strategy.renko_box_size}")
    print(f"🛑 Stop Loss: {strategy.stop_loss_percentage}% of entry price")
    print(f"📝 Paper Trading: {'Enabled' if strategy.paper_trading else 'Disabled'}")
    
    print("\n⚠️ WARNING: Options trading involves very high risk!")
    print("⚠️ NIFTY options can lose 100% value quickly!")
    print("⚠️ Only trade with money you can afford to lose!")
    
    if not strategy.paper_trading:
        print("\n🚨 LIVE TRADING MODE SELECTED!")
        print("🚨 Real money will be at risk!")
        confirm = input("\nType 'CONFIRM LIVE TRADING' to proceed: ").strip()
        if confirm != "CONFIRM LIVE TRADING":
            print("❌ Live trading cancelled for safety.")
            return
    
    final_confirm = input("\nType 'START' to begin: ").strip().upper()
    if final_confirm == "START":
        strategy.run_strategy()
    else:
        print("❌ Strategy cancelled.")

if __name__ == "__main__":
    main()