#!/usr/bin/env python3
"""
NIFTY Options Renko SuperTrend Strategy for OpenAlgo
Author: OpenAlgo Strategy Builder
Version: 1.0

Specialized strategy for NIFTY Options trading with:
- Automatic ATM/OTM option selection
- Dynamic strike price adjustment
- Options-specific risk management
- Real-time Greeks monitoring (if available)
- Expiry management
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openalgo import api
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any, Tuple, List
import json
import math

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NiftyOptionsRenkoStrategy:
    def __init__(self, api_key: str):
        """
        Initialize the NIFTY Options Renko SuperTrend Strategy
        
        Args:
            api_key: OpenAlgo API key
        """
        # API Configuration
        self.api_key = api_key
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # NIFTY Options Specific Configuration
        self.underlying_symbol = "NIFTY"
        self.underlying_exchange = "NSE"
        self.options_exchange = "NFO"
        self.lot_size = 25  # NIFTY lot size (verify current lot size)
        self.product = "MIS"  # Intraday trading
        
        # Options Parameters
        self.option_type = "CE"  # "CE" for Call, "PE" for Put, "BOTH" for both
        self.strike_selection = "ATM"  # "ATM", "OTM1", "OTM2", "ITM1", "ITM2"
        self.strike_offset = 0  # Additional offset from selected strike
        self.expiry_preference = "CURRENT"  # "CURRENT", "NEXT", "MONTHLY"
        
        # Strategy Parameters
        self.renko_box_size = 50  # Larger box size for NIFTY (typically 25-100)
        self.supertrend_period = 10
        self.supertrend_multiplier = 3.0
        self.stop_loss_points = 25  # Adjusted for options volatility
        self.quantity = 1  # Number of lots
        
        # Options-specific Risk Management
        self.max_loss_per_trade = 5000  # Maximum loss per trade in rupees
        self.time_decay_exit_minutes = 30  # Exit if no movement in X minutes
        self.theta_threshold = -50  # Exit if theta exceeds this (if available)
        
        # Trading Configuration
        self.trade_direction = "BOTH"  # "CE", "PE", "BOTH"
        self.auto_strike_adjustment = True  # Auto-adjust strikes based on underlying movement
        self.avoid_expiry_day = True  # Avoid trading on expiry day
        
        # State Variables
        self.position = 0
        self.current_option_symbol = None
        self.current_strike = None
        self.current_expiry = None
        self.entry_price = 0.0
        self.stop_loss_price = 0.0
        self.last_stop_loss_price = 0.0
        self.entry_time = None
        self.underlying_price = 0.0
        self.supertrend_signal_used = False
        self.trade_count = 0
        self.total_pnl = 0.0
        
        # Renko State
        self.renko_bricks = []
        self.current_brick_open = None
        self.current_brick_close = None
        self.renko_trend = None
        
        # Data Management
        self.running = False
        self.start_time = datetime.now()
        self.trade_history = []
        
        # Options Chain Data
        self.options_chain = {}
        self.available_strikes = []
        self.available_expiries = []
        
        logger.info(f"🚀 NIFTY Options Renko Strategy Initialized")
        logger.info(f"📊 Underlying: {self.underlying_symbol}")
        logger.info(f"🎯 Option Type: {self.option_type}")
        logger.info(f"🧱 Renko Box Size: {self.renko_box_size}")
        logger.info(f"💰 Lot Size: {self.lot_size}")
        logger.info(f"📈 Quantity: {self.quantity} lots ({self.quantity * self.lot_size} shares)")

    def configure_options_strategy(self, **kwargs):
        """
        Configure options-specific parameters
        
        Available parameters:
        - option_type: "CE", "PE", "BOTH"
        - strike_selection: "ATM", "OTM1", "OTM2", "ITM1", "ITM2"
        - strike_offset: Additional points offset
        - expiry_preference: "CURRENT", "NEXT", "MONTHLY"
        - renko_box_size: Box size for NIFTY (25-100 recommended)
        - stop_loss_points: Stop loss in points
        - quantity: Number of lots
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"📝 Updated {key}: {value}")
            else:
                logger.warning(f"⚠️ Unknown parameter: {key}")

    def get_nifty_price(self) -> Optional[float]:
        """Get current NIFTY index price"""
        try:
            response = self.client.quotes(
                symbol=self.underlying_symbol,
                exchange=self.underlying_exchange
            )
            
            if response and 'data' in response:
                price = float(response['data'].get('ltp', 0))
                self.underlying_price = price
                return price
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting NIFTY price: {e}")
            return None

    def get_historical_data(self, days: int = 7) -> Optional[pd.DataFrame]:
        """Get historical data for NIFTY index"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.underlying_symbol,
                exchange=self.underlying_exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("⚠️ No historical data received for NIFTY")
                return None
                
            return df
            
        except Exception as e:
            logger.error(f"❌ Error getting NIFTY historical data: {e}")
            return None

    def calculate_supertrend(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate SuperTrend indicator for NIFTY"""
        if len(df) < self.supertrend_period:
            return df
            
        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR
        price_diffs = [high - low, 
                      high - close.shift(), 
                      close.shift() - low]
        true_range = pd.concat(price_diffs, axis=1)
        true_range = true_range.abs().max(axis=1)
        atr = true_range.ewm(alpha=1/self.supertrend_period, min_periods=self.supertrend_period).mean()

        hl2 = (high + low) / 2
        final_upperband = upperband = hl2 + (self.supertrend_multiplier * atr)
        final_lowerband = lowerband = hl2 - (self.supertrend_multiplier * atr)

        # Initialize supertrend
        supertrend = [True] * len(df)

        for i in range(1, len(df.index)):
            curr, prev = i, i - 1

            if close.iloc[curr] > final_upperband.iloc[prev]:
                supertrend[curr] = True
            elif close.iloc[curr] < final_lowerband.iloc[prev]:
                supertrend[curr] = False
            else:
                supertrend[curr] = supertrend[prev]

                if supertrend[curr] == True and final_lowerband.iloc[curr] < final_lowerband.iloc[prev]:
                    final_lowerband.iat[curr] = final_lowerband.iat[prev]
                if supertrend[curr] == False and final_upperband.iloc[curr] > final_upperband.iloc[prev]:
                    final_upperband.iat[curr] = final_upperband.iat[prev]

            if supertrend[curr] == True:
                final_upperband.iat[curr] = np.nan
            else:
                final_lowerband.iat[curr] = np.nan

        df['supertrend'] = supertrend
        df['supertrend_upper'] = final_upperband
        df['supertrend_lower'] = final_lowerband
        
        return df

    def update_renko_bricks(self, price: float) -> bool:
        """Update Renko bricks with NIFTY price"""
        if self.current_brick_open is None:
            self.current_brick_open = price
            self.current_brick_close = price
            self.renko_trend = None
            return False

        brick_formed = False
        
        # Check for up brick formation
        if price >= self.current_brick_close + self.renko_box_size:
            num_bricks = int((price - self.current_brick_close) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'up',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'up'
            brick_formed = True
            
        # Check for down brick formation
        elif price <= self.current_brick_close - self.renko_box_size:
            num_bricks = int((self.current_brick_close - price) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'down',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'down'
            brick_formed = True

        # Keep only recent bricks
        if len(self.renko_bricks) > 100:
            self.renko_bricks = self.renko_bricks[-100:]

        return brick_formed

    def get_atm_strike(self, nifty_price: float) -> int:
        """Get ATM strike price for NIFTY"""
        # NIFTY strikes are typically in multiples of 50
        strike_interval = 50
        atm_strike = round(nifty_price / strike_interval) * strike_interval
        return int(atm_strike)

    def get_target_strike(self, nifty_price: float, option_type: str) -> int:
        """Get target strike based on selection criteria"""
        atm_strike = self.get_atm_strike(nifty_price)
        
        if self.strike_selection == "ATM":
            target_strike = atm_strike
        elif self.strike_selection == "OTM1":
            target_strike = atm_strike + 50 if option_type == "CE" else atm_strike - 50
        elif self.strike_selection == "OTM2":
            target_strike = atm_strike + 100 if option_type == "CE" else atm_strike - 100
        elif self.strike_selection == "ITM1":
            target_strike = atm_strike - 50 if option_type == "CE" else atm_strike + 50
        elif self.strike_selection == "ITM2":
            target_strike = atm_strike - 100 if option_type == "CE" else atm_strike + 100
        else:
            target_strike = atm_strike
            
        # Apply additional offset
        target_strike += self.strike_offset
        
        return int(target_strike)

    def get_current_expiry(self) -> str:
        """Get current week/month expiry date"""
        # This is a simplified version - in practice, you'd fetch from options chain
        today = datetime.now()
        
        if self.expiry_preference == "CURRENT":
            # Find next Thursday (weekly expiry)
            days_ahead = 3 - today.weekday()  # Thursday is 3
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            expiry_date = today + timedelta(days=days_ahead)
        elif self.expiry_preference == "NEXT":
            # Next week's expiry
            days_ahead = 3 - today.weekday() + 7
            expiry_date = today + timedelta(days=days_ahead)
        else:  # MONTHLY
            # Last Thursday of current month (simplified)
            if today.day > 25:  # If we're past monthly expiry, get next month
                next_month = today.replace(day=28) + timedelta(days=4)
                expiry_date = next_month - timedelta(days=next_month.weekday() - 3)
            else:
                # Find last Thursday of current month
                last_day = today.replace(day=28) + timedelta(days=4)
                last_day = last_day - timedelta(days=last_day.day)
                expiry_date = last_day - timedelta(days=(last_day.weekday() - 3) % 7)
        
        return expiry_date.strftime("%d%b%y").upper()

    def construct_option_symbol(self, strike: int, option_type: str, expiry: str) -> str:
        """
        Construct option symbol in OpenAlgo format
        Based on the reformat_symbol function in master_contract_db.py
        
        Original format: "NIFTY 24650 CE 28 NOV 24"
        Reformatted: "NIFTY28NOV2424650CE"
        """
        # Format: NIFTY + expiry + strike + option_type
        symbol = f"NIFTY{expiry}{strike}{option_type}"
        return symbol

    def get_option_price(self, option_symbol: str) -> Optional[float]:
        """Get current option price"""
        try:
            response = self.client.quotes(
                symbol=option_symbol,
                exchange=self.options_exchange
            )
            
            if response and 'data' in response:
                return float(response['data'].get('ltp', 0))
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting option price for {option_symbol}: {e}")
            return None

    def is_expiry_day(self) -> bool:
        """Check if today is expiry day"""
        if not self.avoid_expiry_day:
            return False
            
        today = datetime.now()
        # Check if today is Thursday (weekly expiry) or last Thursday of month
        if today.weekday() == 3:  # Thursday
            return True
        return False

    def get_supertrend_signal(self, df: pd.DataFrame) -> Optional[str]:
        """Get SuperTrend signal for NIFTY"""
        if len(df) < 3:
            return None
            
        current_supertrend = df['supertrend'].iloc[-1]
        previous_supertrend = df['supertrend'].iloc[-2]
        
        if current_supertrend and not previous_supertrend:
            return "CE"  # Bullish - buy calls
        elif not current_supertrend and previous_supertrend:
            return "PE"  # Bearish - buy puts
            
        return None

    def should_enter_trade(self, nifty_price: float, df: pd.DataFrame) -> Optional[str]:
        """Determine if we should enter an options trade"""
        # Check if expiry day
        if self.is_expiry_day():
            logger.warning("⚠️ Avoiding trades on expiry day")
            return None
        
        # Initial entry with SuperTrend
        if self.position == 0 and not self.supertrend_signal_used:
            supertrend_signal = self.get_supertrend_signal(df)
            if supertrend_signal:
                if self.trade_direction == "BOTH" or self.trade_direction == supertrend_signal:
                    return supertrend_signal
                    
        # Re-entry logic based on underlying price movement
        elif self.position == 0 and self.last_stop_loss_price > 0:
            if self.trade_direction in ["CE", "BOTH"]:
                if nifty_price >= self.underlying_price + self.renko_box_size:
                    return "CE"
                    
            if self.trade_direction in ["PE", "BOTH"]:
                if nifty_price <= self.underlying_price - self.renko_box_size:
                    return "PE"
                    
        return None

    def should_exit_trade(self, option_price: float) -> Tuple[bool, str]:
        """Check if we should exit current position"""
        if self.position == 0:
            return False, ""
            
        # Stop loss check
        if option_price <= self.stop_loss_price:
            return True, "Stop Loss Hit"
            
        # Time decay check
        if self.entry_time and self.time_decay_exit_minutes > 0:
            time_elapsed = (datetime.now() - self.entry_time).total_seconds() / 60
            if time_elapsed > self.time_decay_exit_minutes and option_price <= self.entry_price * 0.8:
                return True, "Time Decay Exit"
        
        # Maximum loss check
        current_loss = (self.entry_price - option_price) * self.quantity * self.lot_size
        if current_loss >= self.max_loss_per_trade:
            return True, "Max Loss Hit"
            
        return False, ""

    def place_option_order(self, option_type: str, nifty_price: float) -> bool:
        """Place options order"""
        try:
            # Get target strike and expiry
            strike = self.get_target_strike(nifty_price, option_type)
            expiry = self.get_current_expiry()
            option_symbol = self.construct_option_symbol(strike, option_type, expiry)
            
            # Get option price
            option_price = self.get_option_price(option_symbol)
            if not option_price or option_price <= 0:
                logger.error(f"❌ Invalid option price for {option_symbol}")
                return False
            
            # Check if option price is reasonable (basic validation)
            if option_price < 1 or option_price > nifty_price * 0.1:
                logger.warning(f"⚠️ Option price seems unreasonable: {option_price}")
                return False
            
            # Place order
            response = self.client.placesmartorder(
                strategy="NIFTY Options Renko",
                symbol=option_symbol,
                action="BUY",
                exchange=self.options_exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.quantity * self.lot_size,
                position_size=self.quantity * self.lot_size
            )
            
            logger.info(f"📋 Option Order Response: {response}")
            
            # Update position
            self.position = self.quantity * self.lot_size
            self.current_option_symbol = option_symbol
            self.current_strike = strike
            self.current_expiry = expiry
            self.entry_price = option_price
            self.stop_loss_price = option_price * 0.5  # 50% stop loss for options
            self.entry_time = datetime.now()
            self.trade_count += 1
            
            logger.info(f"✅ {option_type} Option Order Placed")
            logger.info(f"📊 Symbol: {option_symbol}")
            logger.info(f"🎯 Strike: {strike}, Entry: {option_price:.2f}")
            logger.info(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error placing option order: {e}")
            return False

    def exit_option_position(self, reason: str = "Stop Loss") -> bool:
        """Exit current options position"""
        if self.position == 0 or not self.current_option_symbol:
            return False
            
        try:
            # Get current option price
            current_price = self.get_option_price(self.current_option_symbol)
            if not current_price:
                logger.error("❌ Could not get current option price for exit")
                return False
            
            # Place exit order
            response = self.client.placesmartorder(
                strategy="NIFTY Options Renko",
                symbol=self.current_option_symbol,
                action="SELL",
                exchange=self.options_exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.position,
                position_size=0
            )
            
            # Calculate P&L
            pnl = (current_price - self.entry_price) * self.position
            self.total_pnl += pnl
            
            # Record trade
            trade_record = {
                'timestamp': datetime.now(),
                'symbol': self.current_option_symbol,
                'strike': self.current_strike,
                'entry_price': self.entry_price,
                'exit_price': current_price,
                'quantity': self.position,
                'pnl': pnl,
                'reason': reason,
                'holding_time': (datetime.now() - self.entry_time).total_seconds() / 60
            }
            self.trade_history.append(trade_record)
            
            logger.info(f"🔄 Option Position Exited - {reason}")
            logger.info(f"💰 Trade P&L: {pnl:.2f}, Total P&L: {self.total_pnl:.2f}")
            
            # Reset position
            self.position = 0
            self.current_option_symbol = None
            self.current_strike = None
            self.entry_price = 0.0
            self.stop_loss_price = 0.0
            self.entry_time = None
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exiting option position: {e}")
            return False

    def print_options_status(self, nifty_price: float, df: pd.DataFrame = None):
        """Print comprehensive options strategy status"""
        print(f"\n{'='*80}")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 NIFTY Price: {nifty_price:.2f}")
        print(f"🧱 Renko Trend: {self.renko_trend or 'None'}")
        print(f"📈 Total Trades: {self.trade_count}")
        print(f"💵 Total P&L: {self.total_pnl:.2f}")
        
        if self.position != 0:
            current_price = self.get_option_price(self.current_option_symbol)
            print(f"🎯 Current Position: {self.current_option_symbol}")
            print(f"🎯 Strike: {self.current_strike}")
            print(f"💰 Entry Price: {self.entry_price:.2f}")
            print(f"💰 Current Price: {current_price:.2f}" if current_price else "💰 Current Price: N/A")
            print(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            
            if current_price:
                unrealized_pnl = (current_price - self.entry_price) * self.position
                print(f"📊 Unrealized P&L: {unrealized_pnl:.2f}")
                
            if self.entry_time:
                holding_time = (datetime.now() - self.entry_time).total_seconds() / 60
                print(f"⏱️ Holding Time: {holding_time:.1f} minutes")
            
        if df is not None and len(df) > 0:
            supertrend_status = "Bullish (CE)" if df['supertrend'].iloc[-1] else "Bearish (PE)"
            print(f"📈 SuperTrend: {supertrend_status}")
            
        if len(self.renko_bricks) > 0:
            last_brick = self.renko_bricks[-1]
            print(f"🧱 Last Brick: {last_brick['trend'].upper()} ({last_brick['open']:.2f} -> {last_brick['close']:.2f})")
            
        print(f"🎯 Next ATM Strike: {self.get_atm_strike(nifty_price)}")
        print(f"📅 Current Expiry: {self.get_current_expiry()}")
        print(f"{'='*80}")

    def run_strategy(self):
        """Main strategy execution loop"""
        logger.info("🎯 Starting NIFTY Options Renko Strategy...")
        logger.info("Press Ctrl+C to stop")
        
        self.running = True
        last_status_time = 0
        
        try:
            while self.running:
                # Get NIFTY price
                nifty_price = self.get_nifty_price()
                if nifty_price is None:
                    time.sleep(5)
                    continue
                
                # Get historical data for SuperTrend
                df = self.get_historical_data()
                if df is None:
                    time.sleep(5)
                    continue
                
                # Calculate SuperTrend
                df = self.calculate_supertrend(df)
                
                # Update Renko bricks
                brick_formed = self.update_renko_bricks(nifty_price)
                if brick_formed:
                    logger.info(f"🧱 New Renko brick formed: {self.renko_trend}")
                
                # Check for exit conditions first
                if self.position != 0:
                    current_option_price = self.get_option_price(self.current_option_symbol)
                    if current_option_price:
                        should_exit, exit_reason = self.should_exit_trade(current_option_price)
                        if should_exit:
                            self.exit_option_position(exit_reason)
                
                # Check for entry conditions
                entry_signal = self.should_enter_trade(nifty_price, df)
                if entry_signal:
                    if self.place_option_order(entry_signal, nifty_price):
                        if not self.supertrend_signal_used:
                            self.supertrend_signal_used = True
                            logger.info("🎯 SuperTrend signal used for initial entry")
                
                # Print status periodically
                current_time = time.time()
                if current_time - last_status_time >= 30:  # Every 30 seconds
                    self.print_options_status(nifty_price, df)
                    last_status_time = current_time
                
                # Wait before next iteration
                time.sleep(1)  # 1-second updates
                
        except KeyboardInterrupt:
            logger.info("\n🛑 Strategy stopped by user")
        except Exception as e:
            logger.error(f"❌ Strategy error: {e}")
        finally:
            # Square off any open positions
            if self.position != 0:
                self.exit_option_position("Strategy Stopped")
            
            self.running = False
            logger.info("🏁 Strategy execution completed")
            self.print_final_summary()

    def print_final_summary(self):
        """Print final performance summary"""
        print(f"\n{'='*80}")
        print(f"📊 NIFTY OPTIONS STRATEGY - FINAL SUMMARY")
        print(f"{'='*80}")
        print(f"📈 Total Trades: {self.trade_count}")
        print(f"💰 Total P&L: {self.total_pnl:.2f}")
        print(f"🧱 Total Renko Bricks: {len(self.renko_bricks)}")
        print(f"⏱️ Total Runtime: {str(datetime.now() - self.start_time).split('.')[0]}")
        
        if self.trade_count > 0:
            avg_pnl = self.total_pnl / self.trade_count
            print(f"📊 Average P&L per Trade: {avg_pnl:.2f}")
            
            # Calculate win rate
            winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl', 0) > 0)
            win_rate = (winning_trades / self.trade_count) * 100 if self.trade_count > 0 else 0
            print(f"🎯 Win Rate: {win_rate:.1f}%")
            
            # Average holding time
            if self.trade_history:
                avg_holding_time = sum(trade.get('holding_time', 0) for trade in self.trade_history) / len(self.trade_history)
                print(f"⏱️ Average Holding Time: {avg_holding_time:.1f} minutes")
        
        print(f"{'='*80}")

def main():
    """Main function for NIFTY Options strategy"""
    print("🎯 NIFTY Options Renko SuperTrend Strategy")
    print("=" * 50)
    
    # Get API key
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    # Initialize strategy
    strategy = NiftyOptionsRenkoStrategy(api_key=api_key)
    
    # Configuration menu
    print("\n📝 Options Strategy Configuration:")
    
    # Option type selection
    print("\nOption Type:")
    print("1. CE (Calls only)")
    print("2. PE (Puts only)")
    print("3. BOTH (Calls and Puts)")
    
    option_choice = input("Enter choice (1-3, default: 3): ").strip()
    if option_choice == "1":
        strategy.configure_options_strategy(option_type="CE", trade_direction="CE")
    elif option_choice == "2":
        strategy.configure_options_strategy(option_type="PE", trade_direction="PE")
    else:
        strategy.configure_options_strategy(option_type="BOTH", trade_direction="BOTH")
    
    # Strike selection
    print("\nStrike Selection:")
    print("1. ATM (At The Money)")
    print("2. OTM1 (1 strike Out of The Money)")
    print("3. OTM2 (2 strikes Out of The Money)")
    print("4. ITM1 (1 strike In The Money)")
    
    strike_choice = input("Enter choice (1-4, default: 1): ").strip()
    strike_map = {"1": "ATM", "2": "OTM1", "3": "OTM2", "4": "ITM1"}
    strike_selection = strike_map.get(strike_choice, "ATM")
    strategy.configure_options_strategy(strike_selection=strike_selection)
    
    # Expiry selection
    print("\nExpiry Preference:")
    print("1. Current week")
    print("2. Next week")
    print("3. Monthly")
    
    expiry_choice = input("Enter choice (1-3, default: 1): ").strip()
    expiry_map = {"1": "CURRENT", "2": "NEXT", "3": "MONTHLY"}
    expiry_preference = expiry_map.get(expiry_choice, "CURRENT")
    strategy.configure_options_strategy(expiry_preference=expiry_preference)
    
    # Quantity
    quantity = input("Enter number of lots (default: 1): ").strip()
    if quantity and quantity.isdigit():
        strategy.configure_options_strategy(quantity=int(quantity))
    
    # Renko box size
    box_size = input("Enter Renko box size for NIFTY (25-100, default: 50): ").strip()
    if box_size and box_size.isdigit() and 25 <= int(box_size) <= 100:
        strategy.configure_options_strategy(renko_box_size=int(box_size))
    
    # Final confirmation
    print(f"\n✅ Strategy Configuration:")
    print(f"📊 Option Type: {strategy.option_type}")
    print(f"🎯 Strike Selection: {strategy.strike_selection}")
    print(f"📅 Expiry: {strategy.expiry_preference}")
    print(f"💰 Quantity: {strategy.quantity} lots ({strategy.quantity * strategy.lot_size} shares)")
    print(f"🧱 Renko Box Size: {strategy.renko_box_size}")
    print(f"🛑 Stop Loss: 50% of entry price")
    
    print("\n⚠️ WARNING: Options trading involves high risk!")
    print("⚠️ Make sure you understand the risks before proceeding.")
    
    confirm = input("\nType 'START' to begin trading: ").strip().upper()
    if confirm == "START":
        strategy.run_strategy()
    else:
        print("❌ Strategy cancelled.")

if __name__ == "__main__":
    main()