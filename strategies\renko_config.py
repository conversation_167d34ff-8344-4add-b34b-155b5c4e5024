#!/usr/bin/env python3
"""
Configuration file for Renko SuperTrend Strategy
Modify these parameters according to your trading preferences
"""

# =============================================================================
# STRATEGY CONFIGURATION
# =============================================================================

# Trading Symbol Configuration
SYMBOL = "RELIANCE"  # Change to your preferred symbol
EXCHANGE = "NSE"     # NSE, BSE, etc.
PRODUCT = "MIS"      # MIS for intraday, CNC for delivery

# Renko Parameters
RENKO_BOX_SIZE = 10  # Box size in points (4-15 recommended range)

# SuperTrend Parameters
SUPERTREND_PERIOD = 10      # Period for ATR calculation
SUPERTREND_MULTIPLIER = 3.0 # Multiplier for SuperTrend bands

# Risk Management
STOP_LOSS_POINTS = 10  # Fixed stop loss in points
QUANTITY = 1           # Trading quantity (start small for testing)

# Trading Direction
# Options: "BUY" (only long trades), "SELL" (only short trades), "BOTH" (both directions)
TRADE_DIRECTION = "BOTH"

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Data Management
MAX_DATA_POINTS = 1000    # Maximum historical data points to keep in memory
PRICE_UPDATE_INTERVAL = 1 # Seconds between price updates (1 = tick simulation)

# Logging Level
# Options: "DEBUG", "INFO", "WARNING", "ERROR"
LOG_LEVEL = "INFO"

# Market Hours (IST)
MARKET_START_TIME = "09:15"
MARKET_END_TIME = "15:30"

# =============================================================================
# BACKTEST SETTINGS
# =============================================================================

# Default backtest period
DEFAULT_BACKTEST_DAYS = 30

# =============================================================================
# SAFETY SETTINGS
# =============================================================================

# Maximum number of trades per day (safety limit)
MAX_TRADES_PER_DAY = 50

# Maximum loss per day (in points)
MAX_DAILY_LOSS = 500

# Enable/Disable paper trading mode (for testing)
PAPER_TRADING = False

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Enable trade notifications
ENABLE_NOTIFICATIONS = True

# Print status every N seconds
STATUS_UPDATE_INTERVAL = 30

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT SCENARIOS
# =============================================================================

# Conservative Configuration
CONSERVATIVE_CONFIG = {
    "renko_box_size": 5,
    "supertrend_period": 14,
    "supertrend_multiplier": 2.5,
    "stop_loss_points": 15,
    "quantity": 1,
    "trade_direction": "BOTH"
}

# Aggressive Configuration
AGGRESSIVE_CONFIG = {
    "renko_box_size": 15,
    "supertrend_period": 7,
    "supertrend_multiplier": 3.5,
    "stop_loss_points": 8,
    "quantity": 2,
    "trade_direction": "BOTH"
}

# Scalping Configuration
SCALPING_CONFIG = {
    "renko_box_size": 4,
    "supertrend_period": 5,
    "supertrend_multiplier": 2.0,
    "stop_loss_points": 6,
    "quantity": 3,
    "trade_direction": "BOTH"
}

# =============================================================================
# SYMBOL-SPECIFIC CONFIGURATIONS
# =============================================================================

# Different configurations for different symbols
SYMBOL_CONFIGS = {
    "RELIANCE": {
        "renko_box_size": 10,
        "stop_loss_points": 10,
        "quantity": 1
    },
    "TATASTEEL": {
        "renko_box_size": 5,
        "stop_loss_points": 8,
        "quantity": 2
    },
    "NIFTY": {
        "renko_box_size": 15,
        "stop_loss_points": 15,
        "quantity": 1
    },
    "BANKNIFTY": {
        "renko_box_size": 25,
        "stop_loss_points": 25,
        "quantity": 1
    }
}

def get_symbol_config(symbol: str) -> dict:
    """Get configuration for specific symbol"""
    return SYMBOL_CONFIGS.get(symbol, {
        "renko_box_size": RENKO_BOX_SIZE,
        "stop_loss_points": STOP_LOSS_POINTS,
        "quantity": QUANTITY
    })

def get_config_by_name(config_name: str) -> dict:
    """Get predefined configuration by name"""
    configs = {
        "conservative": CONSERVATIVE_CONFIG,
        "aggressive": AGGRESSIVE_CONFIG,
        "scalping": SCALPING_CONFIG
    }
    return configs.get(config_name.lower(), {})