import time
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from openalgo import api

# OpenAlgo API Configuration
API_KEY = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzQxMTUzNjg1LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTAwMDU3MDEwNiJ9.2FhnwEL5acJInkOYorNxQ94DUkgvd6SiNKLHwRuFP-vNsQ5-1r5PswkWeCV2uHaS0bawN3vfLApvBUhwGQU2oA"
API_URL = "http://localhost:5000/api/v1"
STRATEGY_NAME = "Renko Strategy"
INDEX_SYMBOL = "NIFTY"
EXCHANGE = "NSE"
PRODUCT = "MIS"
QUANTITY = 1
BRICK_SIZE = 5  # Renko brick size
TIMEFRAME = 1  # Timeframe in seconds
MARKET_OPEN_TIME = "09:15:00"
EXPIRY_DATE = "2025-03-06"

# Initialize API Client
client = api(api_key=API_KEY, host=API_URL)

def get_market_price(symbol):
    """Fetch the latest market price."""
    response = client.get_ltp(symbol=symbol, exchange=EXCHANGE)
    return response.get("ltp", None) if response else None

def create_renko(df, brick_size):
    """Generate Renko bricks based on price movements."""
    df["change"] = df["close"].diff()
    df["bricks"] = np.floor(df["change"] / brick_size)
    df["renko"] = df["bricks"].cumsum()
    return df

def get_option_symbol():
    """Determine the closest 2 ITM options based on market open price."""
    nifty_open_price = get_market_price(INDEX_SYMBOL)
    if nifty_open_price:
        strike_price = round(nifty_open_price / 50) * 50
        call_strike = strike_price - 100
        put_strike = strike_price + 100
        return f"NIFTY{call_strike}CE", f"NIFTY{put_strike}PE", call_strike, put_strike
    return None, None, None, None

def place_order(symbol, order_type, strike_price, option_type):
    """Place an order in OpenAlgo."""
    response = client.placesmartorder(
        strategy=STRATEGY_NAME,
        symbol=symbol,
        action="BUY" if order_type == "BUY" else "SELL",
        exchange=EXCHANGE,
        price_type="MARKET",
        product=PRODUCT,
        quantity=QUANTITY,
        position_size=QUANTITY
    )
    return response

# Wait for market to open
while True:
    if datetime.now().strftime("%H:%M:%S") >= MARKET_OPEN_TIME:
        call_option, put_option, call_strike, put_strike = get_option_symbol()
        if call_option and put_option:
            print(f"Trading Options: CALL = {call_option}, PUT = {put_option}")
            break
    time.sleep(1)

# Main strategy loop
market_data = []
current_position = None
active_symbol = None

def renko_strategy():
    """Renko-based trading strategy execution."""
    global current_position, active_symbol
    while True:
        try:
            option_price = get_market_price(active_symbol) if active_symbol else None
            if option_price:
                market_data.append(option_price)
                if len(market_data) > 20:
                    df = pd.DataFrame(market_data, columns=["close"])
                    df = create_renko(df, BRICK_SIZE)

                    if df["renko"].iloc[-1] > df["renko"].iloc[-2]:
                        if current_position == "SELL":
                            print(f"Opposite signal detected - Exiting {active_symbol} and entering {call_option}")
                        print(f"BUY Signal - Placing order for {call_option}")
                        place_order(call_option, "BUY", call_strike, "CE")
                        current_position = "BUY"
                        active_symbol = call_option
                    elif df["renko"].iloc[-1] < df["renko"].iloc[-2]:
                        if current_position == "BUY":
                            print(f"Opposite signal detected - Exiting {active_symbol} and entering {put_option}")
                        print(f"SELL Signal - Placing order for {put_option}")
                        place_order(put_option, "BUY", put_strike, "PE")
                        current_position = "SELL"
                        active_symbol = put_option
        except Exception as e:
            print(f"Error in strategy: {str(e)}")
        time.sleep(TIMEFRAME)

if __name__ == "__main__":
    print("Starting Renko Strategy...")
    renko_strategy()
