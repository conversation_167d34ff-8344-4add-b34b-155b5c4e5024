import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from broker.dhan.api.data import DhanD<PERSON>
from broker.dhan.api.order_api import DhanOrder

class RenkoStrategy:
    def __init__(self):
        self.api_key = None
        self.symbol = None
        self.exchange = None
        self.product = None
        self.quantity = None
        self.position = 0
        self.brick_size = 5  # 5 points brick size
        self.current_brick_open = None
        self.current_brick_close = None
        self.trend = None  # 'up' or 'down'
        self.last_price = None
        self.data_client = None
        self.order_client = None
        
    def initialize(self, api_key, symbol, exchange, product, quantity):
        """Initialize strategy parameters"""
        self.api_key = api_key
        self.symbol = symbol
        self.exchange = exchange
        self.product = product
        self.quantity = quantity
        self.data_client = DhanData(api_key)
        self.order_client = DhanOrder(api_key)
        
    def calculate_renko(self, price):
        """Calculate Renko brick formation"""
        if self.current_brick_open is None:
            self.current_brick_open = price
            self.current_brick_close = price
            self.trend = None
            return False
        
        brick_formed = False
        if self.trend is None or self.trend == 'up':
            if price >= self.current_brick_close + self.brick_size:
                # New up brick
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.brick_size
                self.trend = 'up'
                brick_formed = True
            elif price <= self.current_brick_open - self.brick_size:
                # Trend reversal to down
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.brick_size
                self.trend = 'down'
                brick_formed = True
                
        if self.trend == 'down':
            if price <= self.current_brick_close - self.brick_size:
                # New down brick
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.brick_size
                brick_formed = True
            elif price >= self.current_brick_open + self.brick_size:
                # Trend reversal to up
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.brick_size
                self.trend = 'up'
                brick_formed = True
                
        return brick_formed
        
    def generate_signal(self):
        """Generate trading signal based on Renko pattern"""
        if self.trend == 'up' and self.position <= 0:
            return 'BUY'
        elif self.trend == 'down' and self.position >= 0:
            return 'SELL'
        return None
        
    def execute_trade(self, signal):
        """Execute the trade based on signal"""
        if signal == 'BUY':
            # Place buy order
            order_params = {
                'symbol': self.symbol,
                'exchange': self.exchange,
                'product': self.product,
                'quantity': self.quantity,
                'side': 'BUY',
                'order_type': 'MARKET'
            }
            self.order_client.place_order(**order_params)
            self.position = self.quantity
            
        elif signal == 'SELL':
            # Place sell order
            order_params = {
                'symbol': self.symbol,
                'exchange': self.exchange,
                'product': self.product,
                'quantity': self.quantity,
                'side': 'SELL',
                'order_type': 'MARKET'
            }
            self.order_client.place_order(**order_params)
            self.position = -self.quantity
            
    def is_market_open(self):
        """Check if market is open"""
        current_time = datetime.now().time()
        market_start = datetime.strptime('09:15:00', '%H:%M:%S').time()
        market_end = datetime.strptime('15:30:00', '%H:%M:%S').time()
        return market_start <= current_time <= market_end
        
    def run(self):
        """Main strategy loop"""
        print(f"Starting Renko Strategy for {self.symbol}")
        
        while True:
            try:
                if not self.is_market_open():
                    if self.position != 0:
                        # Square off position at market close
                        signal = 'SELL' if self.position > 0 else 'BUY'
                        self.execute_trade(signal)
                    time.sleep(60)
                    continue
                
                # Get current market price
                current_data = self.data_client.get_quote(
                    self.symbol,
                    self.exchange
                )
                current_price = current_data['last_price']
                
                # Update Renko chart
                brick_formed = self.calculate_renko(current_price)
                
                if brick_formed:
                    # Generate and execute trading signal
                    signal = self.generate_signal()
                    if signal:
                        self.execute_trade(signal)
                        print(f"Executed {signal} at {current_price}")
                
                # Wait for 5 seconds before next update
                time.sleep(5)
                
            except Exception as e:
                print(f"Error in strategy execution: {str(e)}")
                time.sleep(5)
                continue

if __name__ == "__main__":
    # Strategy parameters
    API_KEY = "YOUR_API_KEY"
    SYMBOL = "NIFTY-INDEX"  # Example symbol
    EXCHANGE = "NSE"
    PRODUCT = "MIS"
    QUANTITY = 1
    
    # Initialize and run strategy
    strategy = RenkoStrategy()
    strategy.initialize(API_KEY, SYMBOL, EXCHANGE, PRODUCT, QUANTITY)
    strategy.run() 