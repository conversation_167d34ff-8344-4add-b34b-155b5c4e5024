#!/usr/bin/env python3
"""
Enhanced Renko SuperTrend Strategy for OpenAlgo
Author: OpenAlgo Strategy Builder
Version: 2.0

Features:
- Configurable Renko box sizes (4-15)
- SuperTrend filter for initial entries
- Dynamic re-entry logic with 10-point intervals
- Real-time price monitoring (1-second simulation)
- Comprehensive logging and status reporting
- Backtest capability
- Risk management features
- Paper trading mode
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from openalgo import api
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any, Tuple, List
import json

# Import configuration
try:
    from renko_config import *
except ImportError:
    # Fallback configuration if config file is not found
    SYMBOL = "RELIANCE"
    EXCHANGE = "NSE"
    RENKO_BOX_SIZE = 10
    SUPERTREND_PERIOD = 10
    SUPERTREND_MULTIPLIER = 3.0
    STOP_LOSS_POINTS = 10
    QUANTITY = 1
    TRADE_DIRECTION = "BOTH"
    LOG_LEVEL = "INFO"
    MAX_DATA_POINTS = 1000
    PRICE_UPDATE_INTERVAL = 1
    PAPER_TRADING = False

class EnhancedRenkoSuperTrendStrategy:
    def __init__(self, api_key: str, symbol: str = None, exchange: str = None):
        """
        Initialize the Enhanced Renko SuperTrend Strategy
        
        Args:
            api_key: OpenAlgo API key
            symbol: Trading symbol (optional, uses config default)
            exchange: Exchange (optional, uses config default)
        """
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, LOG_LEVEL),
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # API Configuration
        self.api_key = api_key
        self.symbol = symbol or SYMBOL
        self.exchange = exchange or EXCHANGE
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Load symbol-specific configuration
        symbol_config = get_symbol_config(self.symbol) if 'get_symbol_config' in globals() else {}
        
        # Strategy Parameters
        self.renko_box_size = symbol_config.get('renko_box_size', RENKO_BOX_SIZE)
        self.supertrend_period = SUPERTREND_PERIOD
        self.supertrend_multiplier = SUPERTREND_MULTIPLIER
        self.stop_loss_points = symbol_config.get('stop_loss_points', STOP_LOSS_POINTS)
        self.quantity = symbol_config.get('quantity', QUANTITY)
        self.product = PRODUCT
        self.trade_direction = TRADE_DIRECTION
        
        # Advanced Settings
        self.max_data_points = MAX_DATA_POINTS
        self.price_update_interval = PRICE_UPDATE_INTERVAL
        self.paper_trading = PAPER_TRADING
        
        # State Variables
        self.position = 0
        self.entry_price = 0.0
        self.stop_loss_price = 0.0
        self.last_stop_loss_price = 0.0
        self.supertrend_signal_used = False
        self.trade_count = 0
        self.total_pnl = 0.0
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_pnl = 0.0
        
        # Renko State
        self.renko_bricks = []
        self.current_brick_open = None
        self.current_brick_close = None
        self.renko_trend = None
        
        # Data Management
        self.price_data = []
        self.last_price = 0.0
        self.running = False
        self.start_time = datetime.now()
        
        # Trade History
        self.trade_history = []
        
        # Risk Management
        self.daily_trades = 0
        self.max_trades_per_day = getattr(sys.modules[__name__], 'MAX_TRADES_PER_DAY', 50)
        self.max_daily_loss = getattr(sys.modules[__name__], 'MAX_DAILY_LOSS', 500)
        
        self.logger.info(f"🚀 Enhanced Renko SuperTrend Strategy Initialized")
        self.logger.info(f"📊 Symbol: {self.symbol}, Exchange: {self.exchange}")
        self.logger.info(f"🧱 Renko Box Size: {self.renko_box_size}")
        self.logger.info(f"📈 SuperTrend: Period={self.supertrend_period}, Multiplier={self.supertrend_multiplier}")
        self.logger.info(f"🛑 Stop Loss: {self.stop_loss_points} points")
        self.logger.info(f"💰 Quantity: {self.quantity}")
        self.logger.info(f"🎯 Trade Direction: {self.trade_direction}")
        self.logger.info(f"📝 Paper Trading: {'Enabled' if self.paper_trading else 'Disabled'}")

    def configure_strategy(self, config_name: str = None, **kwargs):
        """
        Configure strategy with predefined or custom parameters
        
        Args:
            config_name: Name of predefined config ("conservative", "aggressive", "scalping")
            **kwargs: Custom parameters to override
        """
        # Load predefined configuration
        if config_name and 'get_config_by_name' in globals():
            config = get_config_by_name(config_name)
            for key, value in config.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    self.logger.info(f"📝 Applied {config_name} config - {key}: {value}")
        
        # Apply custom parameters
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                self.logger.info(f"📝 Updated {key}: {value}")
            else:
                self.logger.warning(f"⚠️ Unknown parameter: {key}")

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            current_time = datetime.now().time()
            market_start = datetime.strptime(MARKET_START_TIME, '%H:%M').time()
            market_end = datetime.strptime(MARKET_END_TIME, '%H:%M').time()
            return market_start <= current_time <= market_end
        except:
            # Fallback to basic market hours
            current_time = datetime.now().time()
            market_start = datetime.strptime('09:15', '%H:%M').time()
            market_end = datetime.strptime('15:30', '%H:%M').time()
            return market_start <= current_time <= market_end

    def get_historical_data(self, days: int = 7) -> Optional[pd.DataFrame]:
        """Get historical data for analysis"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.symbol,
                exchange=self.exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                self.logger.warning("⚠️ No historical data received")
                return None
                
            # Keep only recent data for performance
            if len(df) > self.max_data_points:
                df = df.tail(self.max_data_points)
                
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Error getting historical data: {e}")
            return None

    def get_current_price(self) -> Optional[float]:
        """Get current market price"""
        try:
            response = self.client.quotes(
                symbol=self.symbol,
                exchange=self.exchange
            )
            
            if response and 'data' in response:
                return float(response['data'].get('ltp', 0))
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Error getting current price: {e}")
            return None

    def calculate_supertrend(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate SuperTrend indicator"""
        if len(df) < self.supertrend_period:
            return df
            
        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR
        price_diffs = [high - low, 
                      high - close.shift(), 
                      close.shift() - low]
        true_range = pd.concat(price_diffs, axis=1)
        true_range = true_range.abs().max(axis=1)
        atr = true_range.ewm(alpha=1/self.supertrend_period, min_periods=self.supertrend_period).mean()

        hl2 = (high + low) / 2
        final_upperband = upperband = hl2 + (self.supertrend_multiplier * atr)
        final_lowerband = lowerband = hl2 - (self.supertrend_multiplier * atr)

        # Initialize supertrend
        supertrend = [True] * len(df)

        for i in range(1, len(df.index)):
            curr, prev = i, i - 1

            if close.iloc[curr] > final_upperband.iloc[prev]:
                supertrend[curr] = True
            elif close.iloc[curr] < final_lowerband.iloc[prev]:
                supertrend[curr] = False
            else:
                supertrend[curr] = supertrend[prev]

                if supertrend[curr] == True and final_lowerband.iloc[curr] < final_lowerband.iloc[prev]:
                    final_lowerband.iat[curr] = final_lowerband.iat[prev]
                if supertrend[curr] == False and final_upperband.iloc[curr] > final_upperband.iloc[prev]:
                    final_upperband.iat[curr] = final_upperband.iat[prev]

            if supertrend[curr] == True:
                final_upperband.iat[curr] = np.nan
            else:
                final_lowerband.iat[curr] = np.nan

        df['supertrend'] = supertrend
        df['supertrend_upper'] = final_upperband
        df['supertrend_lower'] = final_lowerband
        
        return df

    def update_renko_bricks(self, price: float) -> bool:
        """Update Renko bricks with new price"""
        if self.current_brick_open is None:
            self.current_brick_open = price
            self.current_brick_close = price
            self.renko_trend = None
            return False

        brick_formed = False
        
        # Check for up brick formation
        if price >= self.current_brick_close + self.renko_box_size:
            num_bricks = int((price - self.current_brick_close) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'up',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'up'
            brick_formed = True
            
        # Check for down brick formation
        elif price <= self.current_brick_close - self.renko_box_size:
            num_bricks = int((self.current_brick_close - price) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'down',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'down'
            brick_formed = True

        # Keep only recent bricks
        if len(self.renko_bricks) > 100:
            self.renko_bricks = self.renko_bricks[-100:]

        return brick_formed

    def get_supertrend_signal(self, df: pd.DataFrame) -> Optional[str]:
        """Get SuperTrend signal for initial entry"""
        if len(df) < 3:
            return None
            
        current_supertrend = df['supertrend'].iloc[-1]
        previous_supertrend = df['supertrend'].iloc[-2]
        
        if current_supertrend and not previous_supertrend:
            return "BUY"
        elif not current_supertrend and previous_supertrend:
            return "SELL"
            
        return None

    def should_enter_trade(self, current_price: float, df: pd.DataFrame) -> Optional[str]:
        """Determine if we should enter a trade"""
        # Risk management checks
        if self.daily_trades >= self.max_trades_per_day:
            self.logger.warning(f"⚠️ Daily trade limit reached: {self.daily_trades}")
            return None
            
        if self.daily_pnl <= -self.max_daily_loss:
            self.logger.warning(f"⚠️ Daily loss limit reached: {self.daily_pnl}")
            return None
        
        # Initial entry with SuperTrend
        if self.position == 0 and not self.supertrend_signal_used:
            supertrend_signal = self.get_supertrend_signal(df)
            if supertrend_signal:
                if self.trade_direction == "BOTH" or self.trade_direction == supertrend_signal:
                    return supertrend_signal
                    
        # Re-entry logic
        elif self.position == 0 and self.last_stop_loss_price > 0:
            if self.trade_direction in ["BUY", "BOTH"]:
                if current_price >= self.last_stop_loss_price + self.stop_loss_points:
                    return "BUY"
                    
            if self.trade_direction in ["SELL", "BOTH"]:
                if current_price <= self.last_stop_loss_price - self.stop_loss_points:
                    return "SELL"
                    
        return None

    def should_exit_trade(self, current_price: float) -> bool:
        """Check if we should exit current position"""
        if self.position == 0:
            return False
            
        if self.position > 0:  # Long position
            return current_price <= self.stop_loss_price
        else:  # Short position
            return current_price >= self.stop_loss_price

    def place_order(self, action: str, current_price: float) -> bool:
        """Place order through OpenAlgo or simulate in paper trading"""
        try:
            if self.paper_trading:
                # Simulate order execution
                self.logger.info(f"📋 PAPER TRADE: {action} order simulated")
                response = {"status": "success", "message": "Paper trade executed"}
            else:
                # Real order execution
                response = self.client.placesmartorder(
                    strategy="Enhanced Renko SuperTrend",
                    symbol=self.symbol,
                    action=action,
                    exchange=self.exchange,
                    price_type="MARKET",
                    product=self.product,
                    quantity=self.quantity,
                    position_size=self.quantity if action == "BUY" else -self.quantity
                )
            
            self.logger.info(f"📋 Order Response: {response}")
            
            # Update position and prices
            if action == "BUY":
                self.position = self.quantity
                self.entry_price = current_price
                self.stop_loss_price = current_price - self.stop_loss_points
            else:  # SELL
                self.position = -self.quantity
                self.entry_price = current_price
                self.stop_loss_price = current_price + self.stop_loss_points
                
            self.trade_count += 1
            self.daily_trades += 1
            
            # Record trade
            trade_record = {
                'timestamp': datetime.now(),
                'action': action,
                'price': current_price,
                'quantity': self.quantity,
                'type': 'ENTRY'
            }
            self.trade_history.append(trade_record)
            
            self.logger.info(f"✅ {action} Order Placed - Entry: {self.entry_price}, SL: {self.stop_loss_price}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error placing {action} order: {e}")
            return False

    def exit_position(self, current_price: float, reason: str = "Stop Loss") -> bool:
        """Exit current position"""
        if self.position == 0:
            return False
            
        try:
            action = "SELL" if self.position > 0 else "BUY"
            
            if self.paper_trading:
                # Simulate exit
                self.logger.info(f"📋 PAPER TRADE: {action} exit simulated")
                response = {"status": "success", "message": "Paper exit executed"}
            else:
                # Real exit
                response = self.client.placesmartorder(
                    strategy="Enhanced Renko SuperTrend",
                    symbol=self.symbol,
                    action=action,
                    exchange=self.exchange,
                    price_type="MARKET",
                    product=self.product,
                    quantity=abs(self.position),
                    position_size=0
                )
            
            # Calculate P&L
            if self.position > 0:  # Was long
                pnl = (current_price - self.entry_price) * self.quantity
            else:  # Was short
                pnl = (self.entry_price - current_price) * abs(self.position)
                
            self.total_pnl += pnl
            self.daily_pnl += pnl
            
            # Update drawdown
            if self.total_pnl > self.peak_pnl:
                self.peak_pnl = self.total_pnl
            drawdown = self.peak_pnl - self.total_pnl
            if drawdown > self.max_drawdown:
                self.max_drawdown = drawdown
            
            # Record exit trade
            trade_record = {
                'timestamp': datetime.now(),
                'action': action,
                'price': current_price,
                'quantity': abs(self.position),
                'type': 'EXIT',
                'pnl': pnl,
                'reason': reason
            }
            self.trade_history.append(trade_record)
            
            self.logger.info(f"🔄 Position Exited - {reason}")
            self.logger.info(f"💰 Trade P&L: {pnl:.2f}, Total P&L: {self.total_pnl:.2f}")
            
            # Store last stop loss price for re-entry logic
            self.last_stop_loss_price = self.stop_loss_price
            
            # Reset position
            self.position = 0
            self.entry_price = 0.0
            self.stop_loss_price = 0.0
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Error exiting position: {e}")
            return False

    def print_detailed_status(self, current_price: float, df: pd.DataFrame = None):
        """Print comprehensive strategy status"""
        print(f"\n{'='*80}")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 Current Price: {current_price:.2f}")
        print(f"📊 Position: {self.position}")
        print(f"🧱 Renko Trend: {self.renko_trend or 'None'}")
        print(f"📈 Total Trades: {self.trade_count} (Daily: {self.daily_trades})")
        print(f"💵 Total P&L: {self.total_pnl:.2f} (Daily: {self.daily_pnl:.2f})")
        print(f"📉 Max Drawdown: {self.max_drawdown:.2f}")
        
        if self.position != 0:
            print(f"🎯 Entry Price: {self.entry_price:.2f}")
            print(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            unrealized_pnl = (current_price - self.entry_price) * self.position
            print(f"📊 Unrealized P&L: {unrealized_pnl:.2f}")
            
        if df is not None and len(df) > 0:
            supertrend_status = "Bullish" if df['supertrend'].iloc[-1] else "Bearish"
            print(f"📈 SuperTrend: {supertrend_status}")
            
        if len(self.renko_bricks) > 0:
            last_brick = self.renko_bricks[-1]
            print(f"🧱 Last Brick: {last_brick['trend'].upper()} ({last_brick['open']:.2f} -> {last_brick['close']:.2f})")
            
        # Runtime statistics
        runtime = datetime.now() - self.start_time
        print(f"⏱️ Runtime: {str(runtime).split('.')[0]}")
        print(f"🎯 SuperTrend Used: {'Yes' if self.supertrend_signal_used else 'No'}")
        print(f"📝 Paper Trading: {'Yes' if self.paper_trading else 'No'}")
        print(f"{'='*80}")

    def save_trade_history(self, filename: str = None):
        """Save trade history to JSON file"""
        if not filename:
            filename = f"trade_history_{self.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            # Convert datetime objects to strings for JSON serialization
            history_for_json = []
            for trade in self.trade_history:
                trade_copy = trade.copy()
                trade_copy['timestamp'] = trade_copy['timestamp'].isoformat()
                history_for_json.append(trade_copy)
            
            with open(filename, 'w') as f:
                json.dump({
                    'symbol': self.symbol,
                    'exchange': self.exchange,
                    'strategy_config': {
                        'renko_box_size': self.renko_box_size,
                        'supertrend_period': self.supertrend_period,
                        'supertrend_multiplier': self.supertrend_multiplier,
                        'stop_loss_points': self.stop_loss_points,
                        'quantity': self.quantity,
                        'trade_direction': self.trade_direction
                    },
                    'performance': {
                        'total_trades': self.trade_count,
                        'total_pnl': self.total_pnl,
                        'max_drawdown': self.max_drawdown,
                        'daily_pnl': self.daily_pnl
                    },
                    'trades': history_for_json
                }, f, indent=2)
            
            self.logger.info(f"💾 Trade history saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"❌ Error saving trade history: {e}")

    def run_strategy(self):
        """Main strategy execution loop"""
        self.logger.info("🎯 Starting Enhanced Renko SuperTrend Strategy...")
        self.logger.info("Press Ctrl+C to stop")
        
        self.running = True
        last_status_time = 0
        
        try:
            while self.running:
                # Check market hours
                if not self.is_market_open():
                    if self.position != 0:
                        current_price = self.get_current_price()
                        if current_price:
                            self.exit_position(current_price, "Market Closed")
                    self.logger.info("🕐 Market is closed. Waiting...")
                    time.sleep(60)
                    continue
                
                # Get historical data for SuperTrend
                df = self.get_historical_data()
                if df is None:
                    time.sleep(5)
                    continue
                
                # Calculate SuperTrend
                df = self.calculate_supertrend(df)
                
                # Get current price
                current_price = self.get_current_price()
                if current_price is None:
                    time.sleep(self.price_update_interval)
                    continue
                    
                self.last_price = current_price
                
                # Update Renko bricks
                brick_formed = self.update_renko_bricks(current_price)
                if brick_formed:
                    self.logger.info(f"🧱 New Renko brick formed: {self.renko_trend}")
                
                # Check for exit conditions first
                if self.should_exit_trade(current_price):
                    self.exit_position(current_price, "Stop Loss Hit")
                
                # Check for entry conditions
                entry_signal = self.should_enter_trade(current_price, df)
                if entry_signal:
                    if self.place_order(entry_signal, current_price):
                        if not self.supertrend_signal_used:
                            self.supertrend_signal_used = True
                            self.logger.info("🎯 SuperTrend signal used for initial entry")
                
                # Print status periodically
                current_time = time.time()
                if current_time - last_status_time >= STATUS_UPDATE_INTERVAL:
                    self.print_detailed_status(current_price, df)
                    last_status_time = current_time
                
                # Wait before next iteration
                time.sleep(self.price_update_interval)
                
        except KeyboardInterrupt:
            self.logger.info("\n🛑 Strategy stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Strategy error: {e}")
        finally:
            # Square off any open positions
            if self.position != 0:
                current_price = self.get_current_price()
                if current_price:
                    self.exit_position(current_price, "Strategy Stopped")
            
            # Save trade history
            self.save_trade_history()
            
            self.running = False
            self.logger.info("🏁 Strategy execution completed")
            
            # Print final summary
            self.print_final_summary()

    def print_final_summary(self):
        """Print final performance summary"""
        print(f"\n{'='*80}")
        print(f"📊 FINAL PERFORMANCE SUMMARY")
        print(f"{'='*80}")
        print(f"📈 Total Trades: {self.trade_count}")
        print(f"💰 Total P&L: {self.total_pnl:.2f}")
        print(f"📉 Max Drawdown: {self.max_drawdown:.2f}")
        print(f"📊 Average P&L per Trade: {self.total_pnl/max(1, self.trade_count):.2f}")
        print(f"🧱 Total Renko Bricks: {len(self.renko_bricks)}")
        print(f"⏱️ Total Runtime: {str(datetime.now() - self.start_time).split('.')[0]}")
        
        if self.trade_count > 0:
            winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl', 0) > 0)
            win_rate = (winning_trades / (self.trade_count // 2)) * 100 if self.trade_count > 0 else 0
            print(f"🎯 Win Rate: {win_rate:.1f}%")
        
        print(f"{'='*80}")

def main():
    """Main function with enhanced user interface"""
    print("🎯 Enhanced Renko SuperTrend Strategy for OpenAlgo")
    print("=" * 60)
    
    # Get API key
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    # Get symbol and exchange
    symbol = input(f"Enter symbol (default: {SYMBOL}): ").strip() or SYMBOL
    exchange = input(f"Enter exchange (default: {EXCHANGE}): ").strip() or EXCHANGE
    
    # Initialize strategy
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol, exchange=exchange)
    
    # Configuration menu
    print("\n📝 Configuration Options:")
    print("1. Use default configuration")
    print("2. Use predefined configuration (conservative/aggressive/scalping)")
    print("3. Custom configuration")
    
    config_choice = input("Enter choice (1-3): ").strip()
    
    if config_choice == "2":
        print("\nPredefined Configurations:")
        print("- conservative: Lower risk, stable returns")
        print("- aggressive: Higher risk, potentially higher returns")
        print("- scalping: Quick trades, small profits")
        
        config_name = input("Enter configuration name: ").strip()
        strategy.configure_strategy(config_name)
        
    elif config_choice == "3":
        print("\nCustom Configuration:")
        
        box_size = input(f"Renko box size (4-15, default: {strategy.renko_box_size}): ").strip()
        if box_size and box_size.isdigit() and 4 <= int(box_size) <= 15:
            strategy.configure_strategy(renko_box_size=int(box_size))
        
        direction = input("Trade direction (BUY/SELL/BOTH, default: BOTH): ").strip().upper()
        if direction in ["BUY", "SELL", "BOTH"]:
            strategy.configure_strategy(trade_direction=direction)
        
        quantity = input(f"Quantity (default: {strategy.quantity}): ").strip()
        if quantity and quantity.isdigit():
            strategy.configure_strategy(quantity=int(quantity))
        
        paper_trading = input("Enable paper trading? (y/n, default: n): ").strip().lower()
        if paper_trading == 'y':
            strategy.configure_strategy(paper_trading=True)
    
    # Run mode selection
    print("\n🚀 Select Mode:")
    print("1. Live Trading")
    print("2. Paper Trading")
    print("3. Backtest")
    
    mode = input("Enter choice (1-3): ").strip()
    
    if mode == "2":
        strategy.configure_strategy(paper_trading=True)
        strategy.run_strategy()
    elif mode == "3":
        start_date = input("Enter start date (YYYY-MM-DD): ").strip()
        end_date = input("Enter end date (YYYY-MM-DD): ").strip()
        if start_date and end_date:
            strategy.backtest_mode(start_date, end_date)
        else:
            print("❌ Valid dates are required for backtesting!")
    else:
        print("\n⚠️ WARNING: You are about to start LIVE TRADING!")
        print("Make sure you have tested the strategy thoroughly.")
        confirm = input("Type 'CONFIRM' to proceed with live trading: ").strip()
        if confirm == "CONFIRM":
            strategy.run_strategy()
        else:
            print("❌ Live trading cancelled.")

if __name__ == "__main__":
    main()