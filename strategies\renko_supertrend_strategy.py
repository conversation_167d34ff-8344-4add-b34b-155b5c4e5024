#!/usr/bin/env python3
"""
Advanced Renko SuperTrend Strategy for OpenAlgo
Author: OpenAlgo Strategy Builder
Description: Renko-based strategy with SuperTrend filter and dynamic re-entry logic
"""

from openalgo import api
import pandas as pd
import numpy as np
import time
import threading
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict, Any, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RenkoSuperTrendStrategy:
    def __init__(self, api_key: str, symbol: str = "RELIANCE", exchange: str = "NSE"):
        """
        Initialize the Renko SuperTrend Strategy
        
        Args:
            api_key: OpenAlgo API key
            symbol: Trading symbol
            exchange: Exchange (NSE, BSE, etc.)
        """
        # API Configuration
        self.api_key = api_key
        self.symbol = symbol
        self.exchange = exchange
        self.client = api(api_key=self.api_key, host='http://127.0.0.1:5000')
        
        # Strategy Parameters (Configurable)
        self.renko_box_size = 10  # Default box size, can be changed (4-15 range)
        self.supertrend_period = 10
        self.supertrend_multiplier = 3.0
        self.stop_loss_points = 10  # Fixed 10 points stop loss
        self.quantity = 1  # Fixed quantity
        self.product = "MIS"  # Intraday trading
        
        # Trading Configuration
        self.trade_direction = "BOTH"  # Options: "BUY", "SELL", "BOTH"
        self.max_data_points = 1000  # For performance optimization
        
        # State Variables
        self.position = 0  # 0 = no position, 1 = long, -1 = short
        self.entry_price = 0.0
        self.stop_loss_price = 0.0
        self.last_stop_loss_price = 0.0  # For re-entry calculation
        self.supertrend_signal_used = False  # Track if SuperTrend was used for entry
        self.trade_count = 0
        self.total_pnl = 0.0
        
        # Renko State
        self.renko_bricks = []
        self.current_brick_open = None
        self.current_brick_close = None
        self.renko_trend = None  # 'up' or 'down'
        
        # Data Management
        self.price_data = []
        self.last_price = 0.0
        self.running = False
        
        logger.info(f"🚀 Initialized Renko SuperTrend Strategy")
        logger.info(f"📊 Symbol: {self.symbol}, Exchange: {self.exchange}")
        logger.info(f"🧱 Renko Box Size: {self.renko_box_size}")
        logger.info(f"📈 SuperTrend: Period={self.supertrend_period}, Multiplier={self.supertrend_multiplier}")
        logger.info(f"🛑 Stop Loss: {self.stop_loss_points} points")
        logger.info(f"💰 Quantity: {self.quantity}")
        logger.info(f"🎯 Trade Direction: {self.trade_direction}")

    def configure_strategy(self, **kwargs):
        """
        Configure strategy parameters
        
        Available parameters:
        - renko_box_size: Renko box size (4-15)
        - supertrend_period: SuperTrend period
        - supertrend_multiplier: SuperTrend multiplier
        - stop_loss_points: Stop loss in points
        - quantity: Trading quantity
        - trade_direction: "BUY", "SELL", or "BOTH"
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"📝 Updated {key}: {value}")
            else:
                logger.warning(f"⚠️ Unknown parameter: {key}")

    def get_historical_data(self, days: int = 7) -> Optional[pd.DataFrame]:
        """Get historical data for analysis"""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            df = self.client.history(
                symbol=self.symbol,
                exchange=self.exchange,
                interval="1m",  # Use 1-minute data for better granularity
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.warning("⚠️ No historical data received")
                return None
                
            # Keep only recent data for performance
            if len(df) > self.max_data_points:
                df = df.tail(self.max_data_points)
                
            return df
            
        except Exception as e:
            logger.error(f"❌ Error getting historical data: {e}")
            return None

    def get_current_price(self) -> Optional[float]:
        """Get current market price using quotes API"""
        try:
            response = self.client.quotes(
                symbol=self.symbol,
                exchange=self.exchange
            )
            
            if response and 'data' in response:
                return float(response['data'].get('ltp', 0))
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting current price: {e}")
            return None

    def calculate_supertrend(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate SuperTrend indicator
        
        Args:
            df: DataFrame with OHLC data
            
        Returns:
            DataFrame with SuperTrend columns added
        """
        if len(df) < self.supertrend_period:
            return df
            
        high = df['high']
        low = df['low']
        close = df['close']

        # Calculate ATR
        price_diffs = [high - low, 
                      high - close.shift(), 
                      close.shift() - low]
        true_range = pd.concat(price_diffs, axis=1)
        true_range = true_range.abs().max(axis=1)
        atr = true_range.ewm(alpha=1/self.supertrend_period, min_periods=self.supertrend_period).mean()

        hl2 = (high + low) / 2
        final_upperband = upperband = hl2 + (self.supertrend_multiplier * atr)
        final_lowerband = lowerband = hl2 - (self.supertrend_multiplier * atr)

        # Initialize supertrend
        supertrend = [True] * len(df)

        for i in range(1, len(df.index)):
            curr, prev = i, i - 1

            if close.iloc[curr] > final_upperband.iloc[prev]:
                supertrend[curr] = True
            elif close.iloc[curr] < final_lowerband.iloc[prev]:
                supertrend[curr] = False
            else:
                supertrend[curr] = supertrend[prev]

                if supertrend[curr] == True and final_lowerband.iloc[curr] < final_lowerband.iloc[prev]:
                    final_lowerband.iat[curr] = final_lowerband.iat[prev]
                if supertrend[curr] == False and final_upperband.iloc[curr] > final_upperband.iloc[prev]:
                    final_upperband.iat[curr] = final_upperband.iat[prev]

            if supertrend[curr] == True:
                final_upperband.iat[curr] = np.nan
            else:
                final_lowerband.iat[curr] = np.nan

        df['supertrend'] = supertrend
        df['supertrend_upper'] = final_upperband
        df['supertrend_lower'] = final_lowerband
        
        return df

    def update_renko_bricks(self, price: float) -> bool:
        """
        Update Renko bricks with new price
        
        Args:
            price: Current price
            
        Returns:
            bool: True if new brick was formed
        """
        if self.current_brick_open is None:
            self.current_brick_open = price
            self.current_brick_close = price
            self.renko_trend = None
            return False

        brick_formed = False
        
        # Check for up brick formation
        if price >= self.current_brick_close + self.renko_box_size:
            # New up brick(s)
            num_bricks = int((price - self.current_brick_close) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open + self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'up',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'up'
            brick_formed = True
            
        # Check for down brick formation
        elif price <= self.current_brick_close - self.renko_box_size:
            # New down brick(s)
            num_bricks = int((self.current_brick_close - price) // self.renko_box_size)
            for _ in range(num_bricks):
                self.current_brick_open = self.current_brick_close
                self.current_brick_close = self.current_brick_open - self.renko_box_size
                self.renko_bricks.append({
                    'open': self.current_brick_open,
                    'close': self.current_brick_close,
                    'trend': 'down',
                    'timestamp': datetime.now()
                })
            self.renko_trend = 'down'
            brick_formed = True

        # Keep only recent bricks for memory management
        if len(self.renko_bricks) > 100:
            self.renko_bricks = self.renko_bricks[-100:]

        return brick_formed

    def get_supertrend_signal(self, df: pd.DataFrame) -> Optional[str]:
        """
        Get SuperTrend signal for initial entry
        
        Args:
            df: DataFrame with SuperTrend data
            
        Returns:
            str: "BUY", "SELL", or None
        """
        if len(df) < 3:
            return None
            
        # Check for SuperTrend signal change
        current_supertrend = df['supertrend'].iloc[-1]
        previous_supertrend = df['supertrend'].iloc[-2]
        
        # Bullish signal: SuperTrend changes from False to True
        if current_supertrend and not previous_supertrend:
            return "BUY"
        # Bearish signal: SuperTrend changes from True to False
        elif not current_supertrend and previous_supertrend:
            return "SELL"
            
        return None

    def should_enter_trade(self, current_price: float, df: pd.DataFrame) -> Optional[str]:
        """
        Determine if we should enter a trade
        
        Args:
            current_price: Current market price
            df: DataFrame with indicators
            
        Returns:
            str: "BUY", "SELL", or None
        """
        # If no position and SuperTrend hasn't been used yet
        if self.position == 0 and not self.supertrend_signal_used:
            supertrend_signal = self.get_supertrend_signal(df)
            if supertrend_signal:
                # Check trade direction configuration
                if self.trade_direction == "BOTH" or self.trade_direction == supertrend_signal:
                    return supertrend_signal
                    
        # Re-entry logic based on price movement from last stop loss
        elif self.position == 0 and self.last_stop_loss_price > 0:
            if self.trade_direction in ["BUY", "BOTH"]:
                # Re-enter BUY if price moves 10 points up from last stop loss
                if current_price >= self.last_stop_loss_price + self.stop_loss_points:
                    return "BUY"
                    
            if self.trade_direction in ["SELL", "BOTH"]:
                # Re-enter SELL if price moves 10 points down from last stop loss
                if current_price <= self.last_stop_loss_price - self.stop_loss_points:
                    return "SELL"
                    
        return None

    def should_exit_trade(self, current_price: float) -> bool:
        """
        Check if we should exit current position
        
        Args:
            current_price: Current market price
            
        Returns:
            bool: True if should exit
        """
        if self.position == 0:
            return False
            
        # Check stop loss
        if self.position > 0:  # Long position
            return current_price <= self.stop_loss_price
        else:  # Short position
            return current_price >= self.stop_loss_price

    def place_order(self, action: str, current_price: float) -> bool:
        """
        Place order through OpenAlgo
        
        Args:
            action: "BUY" or "SELL"
            current_price: Current market price
            
        Returns:
            bool: True if order was successful
        """
        try:
            response = self.client.placesmartorder(
                strategy="Renko SuperTrend Strategy",
                symbol=self.symbol,
                action=action,
                exchange=self.exchange,
                price_type="MARKET",
                product=self.product,
                quantity=self.quantity,
                position_size=self.quantity if action == "BUY" else -self.quantity
            )
            
            logger.info(f"📋 Order Response: {response}")
            
            # Update position and prices
            if action == "BUY":
                self.position = self.quantity
                self.entry_price = current_price
                self.stop_loss_price = current_price - self.stop_loss_points
            else:  # SELL
                self.position = -self.quantity
                self.entry_price = current_price
                self.stop_loss_price = current_price + self.stop_loss_points
                
            self.trade_count += 1
            logger.info(f"✅ {action} Order Placed - Entry: {self.entry_price}, SL: {self.stop_loss_price}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error placing {action} order: {e}")
            return False

    def exit_position(self, current_price: float, reason: str = "Stop Loss") -> bool:
        """
        Exit current position
        
        Args:
            current_price: Current market price
            reason: Reason for exit
            
        Returns:
            bool: True if exit was successful
        """
        if self.position == 0:
            return False
            
        try:
            action = "SELL" if self.position > 0 else "BUY"
            
            response = self.client.placesmartorder(
                strategy="Renko SuperTrend Strategy",
                symbol=self.symbol,
                action=action,
                exchange=self.exchange,
                price_type="MARKET",
                product=self.product,
                quantity=abs(self.position),
                position_size=0  # Square off position
            )
            
            # Calculate P&L
            if self.position > 0:  # Was long
                pnl = (current_price - self.entry_price) * self.quantity
            else:  # Was short
                pnl = (self.entry_price - current_price) * abs(self.position)
                
            self.total_pnl += pnl
            
            logger.info(f"🔄 Position Exited - {reason}")
            logger.info(f"💰 Trade P&L: {pnl:.2f}, Total P&L: {self.total_pnl:.2f}")
            
            # Store last stop loss price for re-entry logic
            self.last_stop_loss_price = self.stop_loss_price
            
            # Reset position
            self.position = 0
            self.entry_price = 0.0
            self.stop_loss_price = 0.0
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exiting position: {e}")
            return False

    def print_status(self, current_price: float, df: pd.DataFrame = None):
        """Print current strategy status"""
        print(f"\n{'='*60}")
        print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 Current Price: {current_price:.2f}")
        print(f"📊 Position: {self.position}")
        print(f"🧱 Renko Trend: {self.renko_trend}")
        print(f"📈 Total Trades: {self.trade_count}")
        print(f"💵 Total P&L: {self.total_pnl:.2f}")
        
        if self.position != 0:
            print(f"🎯 Entry Price: {self.entry_price:.2f}")
            print(f"🛑 Stop Loss: {self.stop_loss_price:.2f}")
            unrealized_pnl = (current_price - self.entry_price) * self.position
            print(f"📊 Unrealized P&L: {unrealized_pnl:.2f}")
            
        if df is not None and len(df) > 0:
            supertrend_status = "Bullish" if df['supertrend'].iloc[-1] else "Bearish"
            print(f"📈 SuperTrend: {supertrend_status}")
            
        if len(self.renko_bricks) > 0:
            last_brick = self.renko_bricks[-1]
            print(f"🧱 Last Brick: {last_brick['trend'].upper()} ({last_brick['open']:.2f} -> {last_brick['close']:.2f})")
            
        print(f"{'='*60}")

    def run_strategy(self):
        """Main strategy execution loop"""
        logger.info("🎯 Starting Renko SuperTrend Strategy...")
        logger.info("Press Ctrl+C to stop")
        
        self.running = True
        
        try:
            while self.running:
                # Get historical data for SuperTrend calculation
                df = self.get_historical_data()
                if df is None:
                    time.sleep(5)
                    continue
                
                # Calculate SuperTrend
                df = self.calculate_supertrend(df)
                
                # Get current price (simulating tick data)
                current_price = self.get_current_price()
                if current_price is None:
                    time.sleep(1)
                    continue
                    
                self.last_price = current_price
                
                # Update Renko bricks
                brick_formed = self.update_renko_bricks(current_price)
                
                # Check for exit conditions first
                if self.should_exit_trade(current_price):
                    self.exit_position(current_price, "Stop Loss Hit")
                
                # Check for entry conditions
                entry_signal = self.should_enter_trade(current_price, df)
                if entry_signal:
                    if self.place_order(entry_signal, current_price):
                        if not self.supertrend_signal_used:
                            self.supertrend_signal_used = True
                            logger.info("🎯 SuperTrend signal used for initial entry")
                
                # Print status every 30 seconds
                if int(time.time()) % 30 == 0:
                    self.print_status(current_price, df)
                
                # Wait 1 second (simulating tick data)
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("\n🛑 Strategy stopped by user")
        except Exception as e:
            logger.error(f"❌ Strategy error: {e}")
        finally:
            # Square off any open positions
            if self.position != 0:
                current_price = self.get_current_price()
                if current_price:
                    self.exit_position(current_price, "Strategy Stopped")
            
            self.running = False
            logger.info("🏁 Strategy execution completed")

    def backtest_mode(self, start_date: str, end_date: str):
        """
        Run strategy in backtest mode
        
        Args:
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
        """
        logger.info(f"📊 Starting backtest from {start_date} to {end_date}")
        
        # Get historical data for backtest period
        try:
            df = self.client.history(
                symbol=self.symbol,
                exchange=self.exchange,
                interval="1m",
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                logger.error("❌ No data available for backtest period")
                return
                
            # Calculate SuperTrend for entire dataset
            df = self.calculate_supertrend(df)
            
            logger.info(f"📈 Processing {len(df)} data points...")
            
            # Simulate strategy execution
            for i, row in df.iterrows():
                current_price = row['close']
                
                # Update Renko bricks
                self.update_renko_bricks(current_price)
                
                # Check exit conditions
                if self.should_exit_trade(current_price):
                    self.exit_position(current_price, "Stop Loss Hit")
                
                # Check entry conditions
                current_df = df.iloc[:i+1]  # Data up to current point
                entry_signal = self.should_enter_trade(current_price, current_df)
                if entry_signal:
                    # Simulate order placement
                    if entry_signal == "BUY":
                        self.position = self.quantity
                        self.entry_price = current_price
                        self.stop_loss_price = current_price - self.stop_loss_points
                    else:  # SELL
                        self.position = -self.quantity
                        self.entry_price = current_price
                        self.stop_loss_price = current_price + self.stop_loss_points
                    
                    self.trade_count += 1
                    if not self.supertrend_signal_used:
                        self.supertrend_signal_used = True
            
            # Final position square off
            if self.position != 0:
                final_price = df['close'].iloc[-1]
                self.exit_position(final_price, "Backtest End")
            
            # Print backtest results
            print(f"\n{'='*60}")
            print(f"📊 BACKTEST RESULTS")
            print(f"{'='*60}")
            print(f"📈 Total Trades: {self.trade_count}")
            print(f"💰 Total P&L: {self.total_pnl:.2f}")
            print(f"📊 Average P&L per Trade: {self.total_pnl/max(1, self.trade_count):.2f}")
            print(f"🧱 Total Renko Bricks: {len(self.renko_bricks)}")
            print(f"{'='*60}")
            
        except Exception as e:
            logger.error(f"❌ Backtest error: {e}")

def main():
    """Main function to run the strategy"""
    print("🎯 Renko SuperTrend Strategy for OpenAlgo")
    print("=" * 50)
    
    # Get configuration from user
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    exchange = input("Enter exchange (default: NSE): ").strip() or "NSE"
    
    # Initialize strategy
    strategy = RenkoSuperTrendStrategy(api_key=api_key, symbol=symbol, exchange=exchange)
    
    # Configuration options
    print("\n📝 Strategy Configuration:")
    renko_box = input(f"Renko box size (4-15, default: {strategy.renko_box_size}): ").strip()
    if renko_box and renko_box.isdigit() and 4 <= int(renko_box) <= 15:
        strategy.configure_strategy(renko_box_size=int(renko_box))
    
    direction = input("Trade direction (BUY/SELL/BOTH, default: BOTH): ").strip().upper()
    if direction in ["BUY", "SELL", "BOTH"]:
        strategy.configure_strategy(trade_direction=direction)
    
    quantity = input(f"Quantity (default: {strategy.quantity}): ").strip()
    if quantity and quantity.isdigit():
        strategy.configure_strategy(quantity=int(quantity))
    
    # Run mode selection
    print("\n🚀 Select Mode:")
    print("1. Live Trading")
    print("2. Backtest")
    
    mode = input("Enter choice (1 or 2): ").strip()
    
    if mode == "2":
        start_date = input("Enter start date (YYYY-MM-DD): ").strip()
        end_date = input("Enter end date (YYYY-MM-DD): ").strip()
        if start_date and end_date:
            strategy.backtest_mode(start_date, end_date)
        else:
            print("❌ Valid dates are required for backtesting!")
    else:
        strategy.run_strategy()

if __name__ == "__main__":
    main()