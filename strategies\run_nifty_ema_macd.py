#!/usr/bin/env python3
"""
Quick Start Script for NIFTY EMA-MACD Options Strategy
=====================================================

This script provides easy-to-use presets for running the NIFTY EMA-MACD strategy
"""

import sys
import os
from nifty_ema_macd_options_strategy import NiftyEMAMACDOptionsStrategy
from nifty_ema_macd_config import *

def quick_start_paper_trading():
    """Quick start with paper trading (safe mode)"""
    print("🚀 Quick Start - Paper Trading Mode")
    print("=" * 40)
    print("✅ Safe mode - No real money at risk")
    print("📊 EMA(9,21,34) + MACD(9,13) signals")
    print("⏰ 1-minute timeframe")
    print("💰 1 lot quantity")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = NiftyEMAMACDOptionsStrategy(api_key=api_key)
    strategy.set_timeframe("1m")
    strategy.set_paper_trading(True)
    strategy.quantity = 1
    
    print("\n✅ Strategy configured for paper trading")
    input("Press Enter to start...")
    strategy.run_strategy()

def quick_start_live_trading():
    """Quick start with live trading"""
    print("🚀 Quick Start - Live Trading Mode")
    print("=" * 40)
    print("⚠️  REAL MONEY AT RISK")
    print("📊 EMA(9,21,34) + MACD(9,13) signals")
    print("⏰ 1-minute timeframe")
    print("💰 1 lot quantity")
    print("=" * 40)
    
    # Safety confirmation
    confirm1 = input("⚠️  This is LIVE TRADING mode. Type 'LIVE' to continue: ").strip()
    if confirm1 != "LIVE":
        print("❌ Live trading cancelled")
        return
    
    confirm2 = input("⚠️  Are you sure you want to trade with real money? (yes/no): ").strip().lower()
    if confirm2 != "yes":
        print("❌ Live trading cancelled")
        return
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = NiftyEMAMACDOptionsStrategy(api_key=api_key)
    strategy.set_timeframe("1m")
    strategy.set_paper_trading(False)
    strategy.quantity = 1
    
    print("\n⚠️  Strategy configured for LIVE trading")
    input("Press Enter to start...")
    strategy.run_strategy()

def custom_configuration():
    """Custom strategy configuration"""
    print("🛠️  Custom Configuration Mode")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = NiftyEMAMACDOptionsStrategy(api_key=api_key)
    
    # Timeframe selection
    print("\n1. Select Timeframe:")
    for i, tf in enumerate(AVAILABLE_TIMEFRAMES, 1):
        print(f"   {i}. {tf}")
    
    try:
        tf_choice = int(input(f"Select (1-{len(AVAILABLE_TIMEFRAMES)}): ").strip())
        if 1 <= tf_choice <= len(AVAILABLE_TIMEFRAMES):
            strategy.set_timeframe(AVAILABLE_TIMEFRAMES[tf_choice - 1])
    except:
        print("Invalid choice, using default 1m")
        strategy.set_timeframe("1m")
    
    # Trading mode
    print("\n2. Trading Mode:")
    print("   1. Paper Trading (Safe)")
    print("   2. Live Trading (Real Money)")
    
    mode_choice = input("Select (1-2): ").strip()
    if mode_choice == "2":
        confirm = input("⚠️  LIVE TRADING - Are you sure? (yes/no): ").strip().lower()
        strategy.set_paper_trading(confirm != "yes")
    else:
        strategy.set_paper_trading(True)
    
    # Quantity
    print(f"\n3. Quantity (1-{MAX_QUANTITY} lots):")
    try:
        quantity = int(input(f"Enter quantity (default={DEFAULT_QUANTITY}): ").strip() or str(DEFAULT_QUANTITY))
        strategy.quantity = max(MIN_QUANTITY, min(MAX_QUANTITY, quantity))
    except:
        strategy.quantity = DEFAULT_QUANTITY
    
    # EMA customization
    print("\n4. EMA Settings (optional):")
    custom_ema = input("Customize EMA periods? (y/n): ").strip().lower()
    if custom_ema == 'y':
        try:
            fast = int(input(f"Fast EMA (default={EMA_FAST}): ").strip() or str(EMA_FAST))
            medium = int(input(f"Medium EMA (default={EMA_MEDIUM}): ").strip() or str(EMA_MEDIUM))
            slow = int(input(f"Slow EMA (default={EMA_SLOW}): ").strip() or str(EMA_SLOW))
            
            if fast < medium < slow:
                strategy.ema_fast = fast
                strategy.ema_medium = medium
                strategy.ema_slow = slow
            else:
                print("❌ Invalid EMA periods, using defaults")
        except:
            print("❌ Invalid input, using default EMA settings")
    
    # MACD customization
    print("\n5. MACD Settings (optional):")
    custom_macd = input("Customize MACD periods? (y/n): ").strip().lower()
    if custom_macd == 'y':
        try:
            macd_fast = int(input(f"MACD Fast (default={MACD_FAST}): ").strip() or str(MACD_FAST))
            macd_slow = int(input(f"MACD Slow (default={MACD_SLOW}): ").strip() or str(MACD_SLOW))
            
            if macd_fast < macd_slow:
                strategy.macd_fast = macd_fast
                strategy.macd_slow = macd_slow
            else:
                print("❌ Invalid MACD periods, using defaults")
        except:
            print("❌ Invalid input, using default MACD settings")
    
    # Risk management
    print("\n6. Risk Management (optional):")
    custom_risk = input("Customize risk settings? (y/n): ").strip().lower()
    if custom_risk == 'y':
        try:
            sl_points = int(input(f"Stop Loss Points (default={STOP_LOSS_POINTS}): ").strip() or str(STOP_LOSS_POINTS))
            trail_points = int(input(f"Trailing Trigger Points (default={TRAILING_TRIGGER_POINTS}): ").strip() or str(TRAILING_TRIGGER_POINTS))
            
            if sl_points > 0 and trail_points > 0:
                strategy.stop_loss_points = sl_points
                strategy.trailing_trigger_points = trail_points
            else:
                print("❌ Invalid risk settings, using defaults")
        except:
            print("❌ Invalid input, using default risk settings")
    
    # Final configuration summary
    print(f"\n✅ Strategy Configuration:")
    print(f"   📊 Timeframe: {strategy.timeframe}")
    print(f"   📝 Mode: {'PAPER' if strategy.paper_trading else 'LIVE'}")
    print(f"   💰 Quantity: {strategy.quantity} lots")
    print(f"   📈 EMA: {strategy.ema_fast}/{strategy.ema_medium}/{strategy.ema_slow}")
    print(f"   📊 MACD: {strategy.macd_fast}/{strategy.macd_slow}")
    print(f"   🛑 Stop Loss: {strategy.stop_loss_points} points")
    print(f"   📈 Trailing: {strategy.trailing_trigger_points} points")
    
    if not strategy.paper_trading:
        final_confirm = input("\n⚠️  FINAL CONFIRMATION - Start LIVE trading? (yes/no): ").strip().lower()
        if final_confirm != "yes":
            print("❌ Strategy cancelled")
            return
    
    input("\nPress Enter to start the strategy...")
    strategy.run_strategy()

def strategy_backtest():
    """Run strategy backtest (placeholder)"""
    print("📊 Strategy Backtesting")
    print("=" * 30)
    print("🚧 Backtesting feature coming soon!")
    print("📈 Will include:")
    print("   • Historical performance analysis")
    print("   • Risk metrics calculation")
    print("   • Optimization suggestions")
    print("   • Performance reports")

def view_strategy_help():
    """Display strategy help and documentation"""
    print("📚 NIFTY EMA-MACD Options Strategy Help")
    print("=" * 45)
    print("\n🎯 Strategy Overview:")
    print("   This strategy combines EMA trend analysis with MACD momentum")
    print("   signals to trade NIFTY options with high probability setups.")
    print("\n📊 Entry Conditions:")
    print("   CE (Call) Entry:")
    print("   • EMA 9 > EMA 21 > EMA 34 (all rising)")
    print("   • MACD bullish crossover (MACD > Signal)")
    print("   • Execute ITM/ATM Call options")
    print("\n   PE (Put) Entry:")
    print("   • EMA 9 < EMA 21 < EMA 34 (all falling)")
    print("   • MACD bearish crossover (MACD < Signal)")
    print("   • Execute ITM/ATM Put options")
    print("\n🛑 Risk Management:")
    print("   • Stop Loss: 15 points or swing-based")
    print("   • Trailing SL: Activates after 10 points profit")
    print("   • Position sizing: Configurable lots")
    print("\n⏰ Timeframes:")
    print("   • Supported: 1m, 2m, 3m, 5m")
    print("   • Recommended: 1m for scalping, 5m for swing")
    print("\n💡 Tips:")
    print("   • Start with paper trading")
    print("   • Test different timeframes")
    print("   • Monitor during trending markets")
    print("   • Avoid during high volatility events")

def main():
    """Main menu"""
    while True:
        print("\n🎯 NIFTY EMA-MACD Options Strategy")
        print("=" * 40)
        print("1. 📝 Quick Start - Paper Trading")
        print("2. 💰 Quick Start - Live Trading")
        print("3. 🛠️  Custom Configuration")
        print("4. 📊 Strategy Backtest")
        print("5. 📚 Help & Documentation")
        print("6. ❌ Exit")
        print("=" * 40)
        
        choice = input("Select option (1-6): ").strip()
        
        if choice == "1":
            quick_start_paper_trading()
        elif choice == "2":
            quick_start_live_trading()
        elif choice == "3":
            custom_configuration()
        elif choice == "4":
            strategy_backtest()
        elif choice == "5":
            view_strategy_help()
        elif choice == "6":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice, please try again")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
