#!/usr/bin/env python3
"""
Quick Launcher for NIFTY Options Renko Strategy
Simple interface for NIFTY options trading
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nifty_options_enhanced import EnhancedNiftyOptionsStrategy

def quick_start_ce():
    """Quick start for NIFTY CE (Call) options only"""
    print("🚀 Quick Start - NIFTY CE Options")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy(
        option_type="CE",
        trade_direction="CE",
        strike_selection="ATM",
        expiry_preference="CURRENT",
        paper_trading=True
    )
    
    print(f"\n✅ NIFTY CE Options Strategy Ready")
    print("📝 Paper trading ENABLED for safety")
    print("🎯 ATM Call options, Current week expiry")
    print("🧱 Renko box size: 50 points")
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def quick_start_pe():
    """Quick start for NIFTY PE (Put) options only"""
    print("🚀 Quick Start - NIFTY PE Options")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy(
        option_type="PE",
        trade_direction="PE",
        strike_selection="ATM",
        expiry_preference="CURRENT",
        paper_trading=True
    )
    
    print(f"\n✅ NIFTY PE Options Strategy Ready")
    print("📝 Paper trading ENABLED for safety")
    print("🎯 ATM Put options, Current week expiry")
    print("🧱 Renko box size: 50 points")
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def quick_start_both():
    """Quick start for both CE and PE options"""
    print("🚀 Quick Start - NIFTY CE & PE Options")
    print("=" * 45)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy(
        option_type="BOTH",
        trade_direction="BOTH",
        strike_selection="ATM",
        expiry_preference="CURRENT",
        paper_trading=True
    )
    
    print(f"\n✅ NIFTY Options Strategy Ready")
    print("📝 Paper trading ENABLED for safety")
    print("🎯 ATM Call & Put options, Current week expiry")
    print("🧱 Renko box size: 50 points")
    print("📈 SuperTrend will decide CE or PE entry")
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def conservative_mode():
    """Conservative options trading"""
    print("🛡️ Conservative NIFTY Options Mode")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy("conservative")
    
    print(f"\n✅ Conservative Options Strategy Ready")
    print("🛡️ Lower risk settings")
    print("🎯 ATM options with wider stops")
    print("⏱️ Longer holding time allowed")
    print("📝 Paper trading enabled by default")
    
    paper = input("\nKeep paper trading enabled? (y/n, recommended: y): ").strip().lower()
    if paper != 'n':
        strategy.configure_strategy(paper_trading=True)
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def aggressive_mode():
    """Aggressive options trading"""
    print("⚡ Aggressive NIFTY Options Mode")
    print("=" * 40)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy("aggressive")
    
    print(f"\n✅ Aggressive Options Strategy Ready")
    print("⚡ Higher risk, higher reward settings")
    print("🎯 OTM options with tighter stops")
    print("⏱️ Quick exits on time decay")
    print("💰 2 lots per trade")
    
    print("\n⚠️ WARNING: Aggressive mode has higher risk!")
    paper = input("Enable paper trading? (y/n, STRONGLY recommended: y): ").strip().lower()
    if paper != 'n':
        strategy.configure_strategy(paper_trading=True)
        print("📝 Paper trading enabled for safety")
    else:
        print("🚨 LIVE TRADING with aggressive settings!")
        confirm = input("Type 'AGGRESSIVE LIVE' to confirm: ").strip()
        if confirm != 'AGGRESSIVE LIVE':
            print("❌ Cancelled for safety")
            return
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def weekly_expiry_mode():
    """Weekly expiry focused trading"""
    print("📅 Weekly Expiry NIFTY Options")
    print("=" * 35)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    strategy.configure_strategy("weekly")
    
    print(f"\n✅ Weekly Options Strategy Ready")
    print("📅 Current week expiry focus")
    print("🎯 OTM1 options for better premium")
    print("⏱️ 1 hour maximum holding time")
    print("🛑 Avoids expiry day trading")
    
    paper = input("\nEnable paper trading? (y/n, default: y): ").strip().lower()
    if paper != 'n':
        strategy.configure_strategy(paper_trading=True)
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def custom_configuration():
    """Custom configuration wizard"""
    print("🔧 Custom NIFTY Options Configuration")
    print("=" * 45)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    strategy = EnhancedNiftyOptionsStrategy(api_key=api_key)
    
    # Option type selection
    print("\n1. Option Type:")
    print("   1. CE (Calls only)")
    print("   2. PE (Puts only)")
    print("   3. BOTH (SuperTrend decides)")
    
    option_choice = input("Select (1-3): ").strip()
    option_map = {"1": ("CE", "CE"), "2": ("PE", "PE"), "3": ("BOTH", "BOTH")}
    if option_choice in option_map:
        opt_type, trade_dir = option_map[option_choice]
        strategy.configure_strategy(option_type=opt_type, trade_direction=trade_dir)
    
    # Strike selection
    print("\n2. Strike Selection:")
    print("   1. ATM (At The Money)")
    print("   2. OTM1 (1 strike Out of Money)")
    print("   3. OTM2 (2 strikes Out of Money)")
    print("   4. ITM1 (1 strike In The Money)")
    
    strike_choice = input("Select (1-4): ").strip()
    strike_map = {"1": "ATM", "2": "OTM1", "3": "OTM2", "4": "ITM1"}
    if strike_choice in strike_map:
        strategy.configure_strategy(strike_selection=strike_map[strike_choice])
    
    # Expiry selection
    print("\n3. Expiry Preference:")
    print("   1. Current week")
    print("   2. Next week")
    print("   3. Monthly expiry")
    
    expiry_choice = input("Select (1-3): ").strip()
    expiry_map = {"1": "CURRENT", "2": "NEXT", "3": "MONTHLY"}
    if expiry_choice in expiry_map:
        strategy.configure_strategy(expiry_preference=expiry_map[expiry_choice])
    
    # Quantity
    print("\n4. Position Size:")
    quantity = input("Number of lots (1-5, default: 1): ").strip()
    if quantity and quantity.isdigit() and 1 <= int(quantity) <= 5:
        strategy.configure_strategy(quantity=int(quantity))
    
    # Renko box size
    print("\n5. Renko Box Size:")
    box_size = input("Box size for NIFTY (25-100, default: 50): ").strip()
    if box_size and box_size.isdigit() and 25 <= int(box_size) <= 100:
        strategy.configure_strategy(renko_box_size=int(box_size))
    
    # Paper trading
    print("\n6. Trading Mode:")
    paper = input("Enable paper trading? (y/n, default: y): ").strip().lower()
    strategy.configure_strategy(paper_trading=(paper != 'n'))
    
    # Show final configuration
    print(f"\n✅ Custom Configuration Complete:")
    print(f"🎯 Option: {strategy.option_type}")
    print(f"🎯 Strike: {strategy.strike_selection}")
    print(f"📅 Expiry: {strategy.expiry_preference}")
    print(f"💰 Lots: {strategy.quantity}")
    print(f"🧱 Box Size: {strategy.renko_box_size}")
    print(f"📝 Paper Trading: {'Yes' if strategy.paper_trading else 'No'}")
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def main():
    """Main menu for NIFTY options strategy"""
    print("🎯 NIFTY Options Renko Strategy Launcher")
    print("=" * 50)
    print("Choose your trading mode:")
    print()
    print("1. 🟢 Quick Start - CE Options (Calls)")
    print("2. 🔴 Quick Start - PE Options (Puts)")
    print("3. 🟡 Quick Start - Both CE & PE")
    print("4. 🛡️ Conservative Mode")
    print("5. ⚡ Aggressive Mode")
    print("6. 📅 Weekly Expiry Focus")
    print("7. 🔧 Custom Configuration")
    print("8. 📚 Advanced (Full Interface)")
    print()
    
    choice = input("Enter your choice (1-8): ").strip()
    
    if choice == "1":
        quick_start_ce()
    elif choice == "2":
        quick_start_pe()
    elif choice == "3":
        quick_start_both()
    elif choice == "4":
        conservative_mode()
    elif choice == "5":
        aggressive_mode()
    elif choice == "6":
        weekly_expiry_mode()
    elif choice == "7":
        custom_configuration()
    elif choice == "8":
        # Import and run the full interface
        from nifty_options_enhanced import main as advanced_main
        advanced_main()
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()