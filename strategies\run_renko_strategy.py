#!/usr/bin/env python3
"""
Quick Launcher for Renko SuperTrend Strategy
This script provides a simple way to launch the strategy with your preferred settings
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from renko_supertrend_enhanced import EnhancedRenkoSuperTrendStrategy

def quick_start():
    """Quick start with minimal configuration"""
    print("🚀 Quick Start - Renko SuperTrend Strategy")
    print("=" * 50)
    
    # Essential configuration only
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    # Initialize with defaults
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol)
    
    # Enable paper trading by default for safety
    strategy.configure_strategy(paper_trading=True)
    
    print(f"\n✅ Strategy configured for {symbol}")
    print("📝 Paper trading is ENABLED for safety")
    print("🧱 Using default Renko box size: 10")
    print("🛑 Using default stop loss: 10 points")
    print("\nPress Ctrl+C to stop the strategy")
    
    input("\nPress Enter to start...")
    
    # Run the strategy
    strategy.run_strategy()

def paper_trading_mode():
    """Run in paper trading mode for testing"""
    print("📝 Paper Trading Mode")
    print("=" * 30)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    box_size = input("Enter Renko box size (4-15, default: 10): ").strip()
    box_size = int(box_size) if box_size.isdigit() and 4 <= int(box_size) <= 15 else 10
    
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol)
    strategy.configure_strategy(
        paper_trading=True,
        renko_box_size=box_size
    )
    
    print(f"\n✅ Paper trading configured for {symbol}")
    print(f"🧱 Renko box size: {box_size}")
    print("📝 All trades will be simulated")
    
    input("\nPress Enter to start paper trading...")
    strategy.run_strategy()

def conservative_mode():
    """Run with conservative settings"""
    print("🛡️ Conservative Trading Mode")
    print("=" * 35)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol)
    strategy.configure_strategy("conservative")
    
    print(f"\n✅ Conservative mode configured for {symbol}")
    print("🧱 Renko box size: 5 (smaller moves)")
    print("🛑 Stop loss: 15 points (wider)")
    print("📈 SuperTrend: 14 period, 2.5 multiplier")
    
    # Ask about paper trading
    paper = input("\nEnable paper trading? (y/n, recommended: y): ").strip().lower()
    if paper == 'y':
        strategy.configure_strategy(paper_trading=True)
        print("📝 Paper trading enabled")
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def aggressive_mode():
    """Run with aggressive settings"""
    print("⚡ Aggressive Trading Mode")
    print("=" * 30)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol)
    strategy.configure_strategy("aggressive")
    
    print(f"\n✅ Aggressive mode configured for {symbol}")
    print("🧱 Renko box size: 15 (larger moves)")
    print("🛑 Stop loss: 8 points (tighter)")
    print("📈 SuperTrend: 7 period, 3.5 multiplier")
    print("💰 Quantity: 2")
    
    # Strongly recommend paper trading for aggressive mode
    paper = input("\nEnable paper trading? (y/n, STRONGLY recommended: y): ").strip().lower()
    if paper != 'n':
        strategy.configure_strategy(paper_trading=True)
        print("📝 Paper trading enabled")
    else:
        print("⚠️ WARNING: Live trading with aggressive settings!")
        confirm = input("Type 'CONFIRM' to proceed: ").strip()
        if confirm != 'CONFIRM':
            print("❌ Cancelled for safety")
            return
    
    input("\nPress Enter to start...")
    strategy.run_strategy()

def backtest_mode():
    """Run backtest"""
    print("📊 Backtest Mode")
    print("=" * 20)
    
    api_key = input("Enter your OpenAlgo API Key: ").strip()
    if not api_key:
        print("❌ API Key is required!")
        return
    
    symbol = input("Enter symbol (default: RELIANCE): ").strip() or "RELIANCE"
    
    # Date selection
    print("\nSelect backtest period:")
    print("1. Last 7 days")
    print("2. Last 30 days")
    print("3. Custom dates")
    
    period_choice = input("Enter choice (1-3): ").strip()
    
    from datetime import datetime, timedelta
    
    if period_choice == "1":
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    elif period_choice == "2":
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    else:
        start_date = input("Enter start date (YYYY-MM-DD): ").strip()
        end_date = input("Enter end date (YYYY-MM-DD): ").strip()
        
        if not start_date or not end_date:
            print("❌ Valid dates are required!")
            return
    
    # Configuration selection
    print("\nSelect configuration:")
    print("1. Default")
    print("2. Conservative")
    print("3. Aggressive")
    
    config_choice = input("Enter choice (1-3): ").strip()
    
    strategy = EnhancedRenkoSuperTrendStrategy(api_key=api_key, symbol=symbol)
    
    if config_choice == "2":
        strategy.configure_strategy("conservative")
    elif config_choice == "3":
        strategy.configure_strategy("aggressive")
    
    print(f"\n📊 Running backtest for {symbol}")
    print(f"📅 Period: {start_date} to {end_date}")
    
    # Run backtest
    strategy.backtest_mode(start_date, end_date)

def main():
    """Main menu"""
    print("🎯 Renko SuperTrend Strategy Launcher")
    print("=" * 40)
    print("Choose your preferred mode:")
    print()
    print("1. 🚀 Quick Start (Paper Trading)")
    print("2. 📝 Paper Trading Mode")
    print("3. 🛡️ Conservative Mode")
    print("4. ⚡ Aggressive Mode")
    print("5. 📊 Backtest Mode")
    print("6. 🔧 Advanced Configuration")
    print()
    
    choice = input("Enter your choice (1-6): ").strip()
    
    if choice == "1":
        quick_start()
    elif choice == "2":
        paper_trading_mode()
    elif choice == "3":
        conservative_mode()
    elif choice == "4":
        aggressive_mode()
    elif choice == "5":
        backtest_mode()
    elif choice == "6":
        # Import and run the full configuration
        from renko_supertrend_enhanced import main as advanced_main
        advanced_main()
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()