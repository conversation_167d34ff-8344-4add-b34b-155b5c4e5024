#!/usr/bin/env python3
"""
Test script to verify Upstox connection through OpenAlgo
This script tests basic connectivity and API functionality
"""

from openalgo import api
import time

def test_upstox_connection():
    """Test basic Upstox connection through OpenAlgo"""
    
    # You'll need to get your API key from OpenAlgo dashboard after login
    # Go to Settings -> API Key to generate one
    api_key = 'your-openalgo-api-key'  # Replace with actual API key
    
    # Initialize OpenAlgo client
    client = api(api_key=api_key, host='http://127.0.0.1:6000')
    
    print("🚀 Testing Upstox Connection through OpenAlgo...")
    print("-" * 50)
    
    try:
        # Test 1: Get account info
        print("📊 Test 1: Getting account information...")
        profile = client.profile()
        print(f"✅ Account Info: {profile}")
        
        # Test 2: Get quote for a sample stock
        print("\n📈 Test 2: Getting quote for RELIANCE...")
        quote = client.quote(symbol="RELIANCE", exchange="NSE")
        print(f"✅ RELIANCE Quote: {quote}")
        
        # Test 3: Get positions
        print("\n💼 Test 3: Getting current positions...")
        positions = client.positions()
        print(f"✅ Positions: {positions}")
        
        # Test 4: Get order book
        print("\n📋 Test 4: Getting order book...")
        orders = client.orderbook()
        print(f"✅ Orders: {orders}")
        
        print("\n🎉 All tests passed! Upstox connection is working properly.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure OpenAlgo is running (http://127.0.0.1:5000)")
        print("2. Ensure you're logged into OpenAlgo dashboard")
        print("3. Verify Upstox broker authentication is complete")
        print("4. Check your OpenAlgo API key is correct")
        print("5. Ensure your Upstox API credentials are properly configured")
        return False

if __name__ == "__main__":
    test_upstox_connection()
